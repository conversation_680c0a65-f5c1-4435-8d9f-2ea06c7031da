FROM library/konajdk/kona-with-font-jdk:8

ARG JAR_FILE
ARG ACTIVE
ADD ${JAR_FILE} ecos-client.jar
RUN sh -c 'touch /ecos-client.jar'
ENV JAVA_OPTS="-Xms1024m -Xmx1024m"
ENV SPRING_PROFILES_ACTIVE=${ACTIVE}
ENV SNOWFLAKE_WORD=1
ENV SNOWFLAKE_DADACENTER=2
ENV SERVER_PORT=20400
ENV XXL_JOB_ACCESS_TOKEN=Qz4&iwdaNkeVLQh&cL
ENV XXL_JOB_EXECUTOR_APPNAME=ecos-client
ENV XXL_JOB_EXECUTOR_LOGPATH=/usr/java/logs/xxl/client
ENV XXL_JOB_EXECUTOR_LOGRETENTIONDAYS=3
ENV CUSTOM_TIMEZONE=GMT+8
ENV CUSTOM_TSDB_ENABLE=false
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone
CMD java $JAVA_OPTS -jar /ecos-client.jar