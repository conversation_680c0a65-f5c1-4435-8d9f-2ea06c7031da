package com.weihengtech.ecos.controller.price;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.model.dos.ClientElePriceWholesaleDO;
import com.weihengtech.ecos.model.dtos.ele.HomeElePriceDetailDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceWholesaleVO;
import com.weihengtech.ecos.service.ele.ClientElePriceWholesaleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 固定电价api
 *
 * <AUTHOR>
 * @date 2025/4/28 16:16
 * @version 1.0
 */
@RestController
@RequestMapping("/ele/price/wholesale")
@Api(tags = {"24_批发电价"})
public class ElePriceWholesaleController {

    @Resource
    private ClientElePriceWholesaleService clientElePriceWholesaleService;

    @GetMapping("/get")
    @ApiOperation(value = "查询家庭批发电价")
    public DataResponse<ClientElePriceWholesaleDO> query(@RequestParam String homeId) {
        ClientElePriceWholesaleDO elePriceWhole = clientElePriceWholesaleService.queryWholesalePrice(homeId);
        return DataResponse.success(elePriceWhole);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新分时电价")
    public DataResponse<HomeElePriceDetailDTO> updateUsePrice (@RequestBody @Valid ElePriceWholesaleVO param) {
        return DataResponse.success(clientElePriceWholesaleService.updateWholesalePrice(param));
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除分时电价")
    public EmptyResponse deleteUsePrice(@RequestParam String homeId) {
        clientElePriceWholesaleService.remove(Wrappers.<ClientElePriceWholesaleDO>lambdaQuery()
                .eq(ClientElePriceWholesaleDO::getHomeId, homeId));
        return EmptyResponse.success();
    }
}
