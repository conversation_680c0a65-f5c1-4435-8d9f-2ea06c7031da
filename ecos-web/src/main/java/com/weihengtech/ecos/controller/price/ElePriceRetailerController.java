package com.weihengtech.ecos.controller.price;

import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.enums.ele.RetailerEnum;
import com.weihengtech.ecos.model.dos.ClientElePriceRetailerDO;
import com.weihengtech.ecos.model.dtos.ele.RetailerElePriceDTO;
import com.weihengtech.ecos.model.dtos.ele.HomeElePriceDetailDTO;
import com.weihengtech.ecos.model.dtos.ele.TibberHomeDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceRetailerVO;
import com.weihengtech.ecos.model.vos.price.RetailerEleVO;
import com.weihengtech.ecos.model.vos.price.TibberEleVO;
import com.weihengtech.ecos.service.ele.ClientElePriceRetailerService;
import com.weihengtech.ecos.service.ele.RetailerService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.utils.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 固定电价api
 *
 * <AUTHOR>
 * @date 2025/4/28 16:16
 * @version 1.0
 */
@RestController
@RequestMapping("/ele/price/retailer")
@Api(tags = {"25_零售商电价"})
public class ElePriceRetailerController {

    @Resource
    private ClientElePriceRetailerService clientElePriceRetailerService;
    @Resource
    private StrategyService strategyService;

    @GetMapping("/get")
    @ApiOperation(value = "查询零售商电价")
    public DataResponse<ClientElePriceRetailerDO> query(@RequestParam String homeId) {
        ClientElePriceRetailerDO elePriceRetailer = clientElePriceRetailerService.queryRetailerPrice(homeId);
        return DataResponse.success(elePriceRetailer);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新零售商电价")
    public DataResponse<HomeElePriceDetailDTO> updateRetailerPrice (@RequestBody @Valid ElePriceRetailerVO param) {
        return DataResponse.success(clientElePriceRetailerService.updateRetailerPrice(param));
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除零售商电价")
    public EmptyResponse deleteRetailerPrice(@RequestParam String homeId) {
        clientElePriceRetailerService.deleteRetailerPrice(homeId);
        return EmptyResponse.success();
    }

    @GetMapping("/tibber/homes")
    @ApiOperation(value = "查询tibber家庭列表")
    public DataResponse<List<TibberHomeDTO>> queryTibberHomes(@RequestParam String token) {
        RetailerService retailerService = strategyService.chooseRetailerService(RetailerEnum.TIBBER.getCode());
        List<TibberHomeDTO> tibberHomeList = retailerService.queryTibberHomes(token);
        return DataResponse.success(tibberHomeList);
    }

    @PostMapping("/tibber/ele")
    @ApiOperation(value = "查询tibber电价信息")
    public DataResponse<List<EleDayAheadPriceDto>> queryTibberEle(@RequestBody @Valid TibberEleVO param) {
        RetailerService retailerService = strategyService.chooseRetailerService(RetailerEnum.TIBBER.getCode());
        ClientElePriceRetailerDO retailParam = ClientElePriceRetailerDO.builder()
                .token(param.getToken())
                .retailerHomeId(param.getTibberHomeId())
                .build();
        RetailerElePriceDTO res = retailerService.queryRetailerElePrice(retailParam, param.getTime(), null);
        return DataResponse.success(res.getPriceList());
    }

    @PostMapping("/price")
    @ApiOperation(value = "统一零售商电价接口")
    public DataResponse<RetailerElePriceDTO> queryRetailerPrice(@RequestBody @Valid RetailerEleVO param) {
        RetailerService retailerService = strategyService.chooseRetailerService(param.getRetailer());
        ClientElePriceRetailerDO retailParam = new ClientElePriceRetailerDO();
        BeanUtils.copyProperties(param, retailParam);
        RetailerElePriceDTO priceInfo = retailerService.queryRetailerElePrice(retailParam, param.getTime(), param.getTimezone());
        return DataResponse.success(priceInfo);
    }
}
