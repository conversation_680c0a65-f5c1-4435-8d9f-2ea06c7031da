package com.weihengtech.ecos.controller.price;

import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.model.dtos.ele.HomeElePriceDetailDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceUseVO;
import com.weihengtech.ecos.service.ele.ClientElePriceUseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 固定电价api
 *
 * <AUTHOR>
 * @date 2025/4/28 16:16
 * @version 1.0
 */
@RestController
@RequestMapping("/ele/price/use")
@Api(tags = {"23_分时电价"})
public class ElePriceUseController {

    @Resource
    private ClientElePriceUseService clientElePriceUseService;

    @GetMapping("/get")
    @ApiOperation(value = "查询家庭分时电价")
    public DataResponse<ElePriceUseVO> query(@RequestParam String homeId) {
        ElePriceUseVO resItem = clientElePriceUseService.queryUsePrice(homeId);
        return DataResponse.success(resItem);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新分时电价")
    public DataResponse<HomeElePriceDetailDTO> updateUsePrice (@RequestBody @Valid ElePriceUseVO param) {
        return DataResponse.success(clientElePriceUseService.updateUsePrice(param));
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除分时电价")
    public EmptyResponse deleteUsePrice(@RequestParam String homeId) {
        clientElePriceUseService.deleteUsePrice(homeId);
        return EmptyResponse.success();
    }
}
