package com.weihengtech.ecos.tasks;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.adapter.CustomizeAdapter;
import com.weihengtech.ecos.api.pojo.vos.EleStrategyPreviewVO;
import com.weihengtech.ecos.enums.ele.StrategyModeEnum;
import com.weihengtech.ecos.enums.thirdpart.PriceUnitEnum;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoEzDto;
import com.weihengtech.ecos.model.dtos.customize.StrategyCustomizeDTO;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzV2Vo;
import com.weihengtech.ecos.prometheus.ScheduledTaskMetrics;
import com.weihengtech.ecos.service.app.ClientCustomizeService;
import com.weihengtech.ecos.service.global.RetryService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 负电价充放策略定时任务
 *
 * <AUTHOR>
 * @date 2025/08/29
 * @version 1.0
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "custom.xxl.job", value = "enable", havingValue = "true")
public class NegativePriceStrategyTask {

	@Resource
	private CustomizeAdapter customizeAdapter;
	@Resource
	private RetryService retryService;
	@Resource
	private HubService hubService;
	@Resource
	private ClientCustomizeService clientCustomizeService;
	@Resource
	private ScheduledTaskMetrics scheduledTaskMetrics;

	@XxlJob("negativePriceStrategyTask")
	public void negativePriceStrategyTask() {
		Integer time = Optional.ofNullable(XxlJobHelper.getJobParam())
				.map(JSONUtil::parseObj)
				.map(i -> i.getInt("time", 0))
				.orElse(0);
		String timezoneStr = Optional.ofNullable(XxlJobHelper.getJobParam())
				.map(JSONUtil::parseObj)
				.map(i -> i.getStr("timezone", "Europe/Amsterdam"))
				.orElse("Europe/Amsterdam");
		printLog(String.format("查询电价时间为:%s，时区为：%s", time, timezoneStr));
		List<String> timezoneList = Arrays.asList(timezoneStr.split(","));

		// 查询已开启feedInProhibition的设备
		List<StrategyCustomizeDTO> feedInProhibitionDevices = clientCustomizeService.getFeedInProhibitionDevicesByTimezone(timezoneList);
		if (CollUtil.isEmpty(feedInProhibitionDevices)) {
			printLog("未开启feedInProhibition的设备");
			return;
		}
		List<Long> deviceIdList = feedInProhibitionDevices.stream().map(StrategyCustomizeDTO::getDeviceId).collect(Collectors.toList());
		List<HybridSinglePhaseDO> deviceInfoList = hubService.getBatchById(false, deviceIdList);
		if (CollUtil.isEmpty(deviceInfoList)) {
			printLog("未查询到设备信息");
			return;
		}
		Set<Long> existsIdSet = deviceInfoList.stream().map(HybridSinglePhaseDO::getId).collect(Collectors.toSet());
		// 对于不存在的设备，不予处理
		feedInProhibitionDevices.removeIf(i -> !existsIdSet.contains(i.getDeviceId()));
		Map<Long, String> ratePowerMap = deviceInfoList.stream()
				.collect(Collectors.toMap(HybridSinglePhaseDO::getId, HybridSinglePhaseDO::getRatedPower));

		for (StrategyCustomizeDTO deviceCus : feedInProhibitionDevices) {
			// 设备时区
			String timezone = Optional.ofNullable(deviceCus.getTimezone()).orElse(timezoneList.get(0));
			// 查询额定功率
			String ratedPowerStr = ratePowerMap.get(deviceCus.getDeviceId());
			if (StrUtil.isBlank(ratedPowerStr)) {
				printLog(String.format("未查询到设备 %s 的额定功率，不自动下发策略", deviceCus.getDeviceId()));
				continue;
			}
			printLog(String.format("设备 %s 开始下发负电价策略：", deviceCus.getDeviceId()));
			int ratedPower = Integer.parseInt(ratedPowerStr.substring(0, ratedPowerStr.length() - 1));
			
			// 查询指定时间的电价
			try {
				EleStrategyPreviewVO param = EleStrategyPreviewVO.builder()
						.homeId(deviceCus.getHomeId())
						.time(time)
						.priceImportType(deviceCus.getPriceImportType())
						.strategyMode(StrategyModeEnum.NEGATIVE.getCode())
						.timezone(timezone)
						.region(deviceCus.getRegion())
						.purchaseTax(deviceCus.getPurchaseTax())
						.ratedPower(ratedPower)
						.priceUnit(PriceUnitEnum.kWh.name())
						.build();
				
				CustomizeInfoEzDto strategyResult = customizeAdapter.queryStrategy(param);
				if (CollUtil.isEmpty(strategyResult.getChargingList())) {
					printLog(String.format("设备 %s 未查询到负电价策略，不自动下发策略", deviceCus.getDeviceId()));
					continue;
				}
				// 下发策略
				retryService.writeCustomizeV2(CustomizeInfoEzV2Vo.builder()
						.timezone(timezone)
						.deviceId(String.valueOf(deviceCus.getDeviceId()))
						.chargeUseMode(1)
						.regularSoc(deviceCus.getBatteryMin())
						.regularFeedIn(deviceCus.getMaxFeedIn())
						.chargingList(strategyResult.getChargingList())
						.dischargingList(strategyResult.getDischargingList())
						.build());
			} catch (Exception e) {
				scheduledTaskMetrics.recordFailure("negativePriceStrategyTask");
				printLog(String.format("下发负电价策略失败：%s", e.getMessage()), e);
			}
		}
	}

	/** 打印日志 */
	private void printLog(String msg, Exception... e) {
		if (e.length == 0) {
			log.info(msg);
			XxlJobHelper.log(msg);
		} else {
			log.error(msg, e);
			XxlJobHelper.log(msg, e);
		}
	}
}