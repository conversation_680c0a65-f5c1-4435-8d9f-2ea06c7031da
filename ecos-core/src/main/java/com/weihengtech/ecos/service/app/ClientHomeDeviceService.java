package com.weihengtech.ecos.service.app;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dos.ClientHomeDeviceDo;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: ecos-server
 * @description: 家庭-设备关联表服务类
 * @author: jiahao.jin
 * @create: 2024-01-21 11:34
 **/
public interface ClientHomeDeviceService extends IService<ClientHomeDeviceDo> {

    boolean saveRelation(String homeId, String deviceId, String deviceName);

    boolean existsOne(String homeId, String deviceId);

    List<ClientHomeDeviceDo> getAllMasterHomeDevice(List<String> timezoneList);

    /**
     * 计算指定时间区间15分钟差值降采的电量数据：电网取电、电网馈网、家庭负载
     * key分别是：CommonConstants.GRID_ELE、CommonConstants.FEED_ELE、CommonConstants.LOAD_ELE
     *
     * @param deviceInfo 设备信息
     * @param startTime 起始时间 时间戳
     * @param endTime 截止时间
     * @return
     */
    Map<String, LinkedHashMap<Long, Object>> calCostBaseEleData(HybridSinglePhaseDO deviceInfo, Long startTime, Long endTime);

    List<ClientHomeDeviceDo> getDevicesByHomeId(Long homeId);

    ClientHomeDo getHomeInfoByDeviceId(Long deviceId);
}
