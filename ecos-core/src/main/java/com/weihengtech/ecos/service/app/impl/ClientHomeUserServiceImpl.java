package com.weihengtech.ecos.service.app.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.common.exception.UnauthorizedException;
import com.weihengtech.ecos.dao.ClientHomeUserMapper;
import com.weihengtech.ecos.model.dos.ClientHomeUserDo;
import com.weihengtech.ecos.service.app.ClientHomeUserService;
import com.weihengtech.ecos.service.user.ClientUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @program: ecos-server
 * @description: 家庭-用户关联表服务实现类
 * @author: jiahao.jin
 * @create: 2024-01-21 11:30
 **/
@Service
@RequiredArgsConstructor
public class ClientHomeUserServiceImpl
        extends ServiceImpl<ClientHomeUserMapper, ClientHomeUserDo>
        implements ClientHomeUserService {

    @Override
    public void checkOwner(String userId, String homeId) {
        ClientHomeUserDo clientHomeUserDo = this.getOne(Wrappers.<ClientHomeUserDo>lambdaQuery()
                .eq(ClientHomeUserDo::getHomeId, homeId)
                .eq(ClientHomeUserDo::getUserId, userId)
                .eq(ClientHomeUserDo::getRelationType, CommonConstants.HOME_OWNER));
        if (clientHomeUserDo == null) {
            log.warn("不是所有者，无权操作该家庭");
            throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_HOME);
        }
    }
}
