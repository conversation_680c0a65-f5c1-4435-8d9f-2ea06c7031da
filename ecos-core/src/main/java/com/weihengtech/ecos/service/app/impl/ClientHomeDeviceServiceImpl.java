package com.weihengtech.ecos.service.app.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.dao.ClientHomeDeviceMapper;
import com.weihengtech.ecos.enums.tsdb.TsdbSampleEnum;
import com.weihengtech.ecos.model.dos.ClientHomeDeviceDo;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.service.app.ClientHomeDeviceService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: ecos-server
 * @description: 家庭用户关联表服务实现类
 * @author: jiahao.jin
 * @create: 2024-01-21 11:35
 **/
@Service
@RequiredArgsConstructor
public class ClientHomeDeviceServiceImpl
        extends ServiceImpl<ClientHomeDeviceMapper, ClientHomeDeviceDo>
        implements ClientHomeDeviceService {

    @Resource
    private SnowFlakeUtil snowFlakeUtil;
    @Resource
    private StrategyService strategyService;

    @Override
    public boolean saveRelation(String homeId, String deviceId, String deviceName) {
        ClientHomeDeviceDo clientHomeDeviceDo = new ClientHomeDeviceDo();
        long homeDeviceId = snowFlakeUtil.generateId();
        long nowTime = System.currentTimeMillis();
        clientHomeDeviceDo.setId(homeDeviceId);
        clientHomeDeviceDo.setHomeId(Long.valueOf(homeId));
        clientHomeDeviceDo.setDeviceId(Long.valueOf(deviceId));
        clientHomeDeviceDo.setDeviceName(deviceName);
        clientHomeDeviceDo.setCreateTime(nowTime);
        clientHomeDeviceDo.setUpdateTime(nowTime);
        return this.save(clientHomeDeviceDo);
    }

    @Override
    public boolean existsOne(String homeId, String deviceId) {
        int count = count(Wrappers.<ClientHomeDeviceDo>lambdaQuery().eq(ClientHomeDeviceDo::getHomeId, homeId)
                .eq(ClientHomeDeviceDo::getDeviceId, deviceId));
        return count > 0;
    }

    @Override
    public List<ClientHomeDeviceDo> getAllMasterHomeDevice(List<String> timezoneList) {
        return baseMapper.getAllMasterHomeDevice(timezoneList);
    }

    @Override
    public Map<String, LinkedHashMap<Long, Object>> calCostBaseEleData(HybridSinglePhaseDO deviceInfo, Long startTime, Long endTime) {
        TimeSeriesDatabaseService tsdbService = strategyService
                .chooseTimeSeriesDatabaseService(deviceInfo);
        Map<String, LinkedHashMap<Long, Object>> baseDataRes = tsdbService.deltaQuery(
                deviceInfo.getDeviceSn(),
                CommonConstants.HOME_ENERGY,
                startTime,
                endTime,
                TsdbSampleEnum.FIFTEEN_MINUTE_NONE
        );
        if (CollUtil.isEmpty(baseDataRes)) {
            return Collections.emptyMap();
        }
        List<Long> timeList = baseDataRes.entrySet().stream().findFirst()
                .map(Map.Entry::getValue)
                .map(LinkedHashMap::keySet)
                .map(i -> i.stream().sorted().collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        LinkedHashMap<Long, Object> pv1Data = baseDataRes.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV);
        LinkedHashMap<Long, Object> pv2Data = baseDataRes.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_PV_TO_GRID);
        LinkedHashMap<Long, Object> batChargeData = baseDataRes.get(TsdbMetricsConstants.BAT_E_TOTAL_CHARGE);
        LinkedHashMap<Long, Object> batDischargeData = baseDataRes.get(TsdbMetricsConstants.BAT_E_TOTAL_DISCHARGE);
        LinkedHashMap<Long, Object> toGridData = baseDataRes.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_TO_GRID);
        LinkedHashMap<Long, Object> fromGridData = baseDataRes.get(TsdbMetricsConstants.ELECTRICITY_E_TOTAL_FROM_GRID);
        Map<String, LinkedHashMap<Long, Object>> resultMap = new LinkedHashMap<>();
        LinkedHashMap<Long, Object> gridData = new LinkedHashMap<>();
        LinkedHashMap<Long, Object> feedData = new LinkedHashMap<>();
        LinkedHashMap<Long, Object> homeLoadData = new LinkedHashMap<>();
        for (Long time : timeList) {
            // 来自光伏
            BigDecimal fromSolar1 = new BigDecimal(pv1Data.getOrDefault(time, "0").toString());
            BigDecimal fromSolar2 = new BigDecimal(pv2Data.getOrDefault(time, "0").toString());
            // 电池充电、放电
            BigDecimal toBattery = new BigDecimal(batChargeData.getOrDefault(time, "0").toString());
            BigDecimal fromBattery = new BigDecimal(batDischargeData.getOrDefault(time, "0").toString());
            // 电网取电、馈电
            BigDecimal toGrid = new BigDecimal(toGridData.getOrDefault(time, "0").toString());
            BigDecimal fromGrid = new BigDecimal(fromGridData.getOrDefault(time, "0").toString());
            // 家庭负载
            BigDecimal allFrom = NumberUtil.add(fromSolar1, fromSolar2, fromBattery, fromGrid);
            BigDecimal load = NumberUtil.round(NumberUtil.sub(allFrom, toBattery, toGrid), 2, RoundingMode.HALF_UP);
            // 1、15分钟电价数据是整点数据（00:00-23:45），电量数据是（00:15-23:59:59）
            // 2、为了保持对齐，电量数据如果15分钟整点，则整体往前偏15分钟，如果是23:59:59，则往前偏14:59
            // 3、注意：这里偏移不会对数据产生错位，因为在差值降采之后，第一个数据的00:15代表的是过去15分钟的差值，
            // 而偏移后00：00代表00：00-15这15分钟的差值，数据是不变的，只是代表该时间段的数据的整点位不同而已
            if (time % 100 == 0) {
                time = time - 60*15;
            } else {
                time = time - (60 * 14 + 59);
            }
            homeLoadData.put(time, load);
            gridData.put(time, fromGrid);
            feedData.put(time, toGrid);
        }
        resultMap.put(CommonConstants.LOAD_ELE, homeLoadData);
        resultMap.put(CommonConstants.GRID_ELE, gridData);
        resultMap.put(CommonConstants.FEED_ELE, feedData);
        return resultMap;
    }

    @Override
    public List<ClientHomeDeviceDo> getDevicesByHomeId(Long homeId) {
        return list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                .eq(ClientHomeDeviceDo::getHomeId, homeId));
    }

    @Override
    public ClientHomeDo getHomeInfoByDeviceId(Long deviceId) {
        return baseMapper.getHomeInfoByDeviceId(deviceId);
    }
}
