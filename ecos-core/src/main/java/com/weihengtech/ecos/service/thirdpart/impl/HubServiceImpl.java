package com.weihengtech.ecos.service.thirdpart.impl;

import com.weihengtech.ecos.api.EcosServiceApi;
import com.weihengtech.ecos.api.pojo.dtos.GlobalDeviceConfigDto;
import com.weihengtech.ecos.api.pojo.dtos.GuideAgreementVersionDto;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundDTO;
import com.weihengtech.ecos.api.pojo.dtos.InstallBoundInfoDTO;
import com.weihengtech.ecos.api.pojo.vos.EcosDeviceConfigQueryVo;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.EmptyResponse;
import com.weihengtech.ecos.common.InResponse;
import com.weihengtech.ecos.model.dtos.charger.ExtInfoDto;
import com.weihengtech.ecos.enums.thirdpart.StepEnum;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.app.BindInfoDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicDesignDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicExportDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSaveVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSwitchVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicTestDTO;
import com.weihengtech.ecos.model.dtos.global.GlobalEnestLatestVersionDto;
import com.weihengtech.ecos.model.dtos.global.GlobalVersionDto;
import com.weihengtech.ecos.model.dtos.global.PageInfoDTO;
import com.weihengtech.ecos.model.dtos.global.ResourceCategoryTreeDto;
import com.weihengtech.ecos.model.dtos.charger.V2ClientChargeRecordDto;
import com.weihengtech.ecos.model.vos.charger.ChargerSaveVO;
import com.weihengtech.ecos.model.vos.thirdpart.SystemInfoUpdVO;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordPageVo;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordSaveVo;
import com.weihengtech.ecos.model.vos.charger.V2ClientChargeRecordUpdateVo;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class HubServiceImpl implements HubService {

    @Resource
    private EcosServiceApi  ecosServiceApi;
    @Override
    public HybridSinglePhaseDO getById(Long id) {
        DataResponse<HybridSinglePhaseDO> resp = ecosServiceApi.queryById(id);
        return parseBean(resp);
    }

    @Override
    public List<HybridSinglePhaseDO> getBatchById(Boolean isNeedExt, List<Long> ids) {
        DataResponse<List<HybridSinglePhaseDO>> resp = ecosServiceApi.queryBatchById(isNeedExt,ids);
        return parseBean(resp);
    }

    @Override
    public HybridSinglePhaseDO getByDeviceName(String deviceName) {
        DataResponse<HybridSinglePhaseDO> resp = ecosServiceApi.queryByDeviceName(deviceName);
        HybridSinglePhaseDO res = parseBean(resp);
        if (res == null) {
            throw new EcosException(EcosExceptionEnum.DEVICE_NOT_FIND);
        }
        return res;
    }

    @Override
    public GuideAgreementVersionDto getAgreementVersion() {
        return ecosServiceApi.getAgreementVersion().getData();
    }

    @Override
    public DataResponse<GlobalDeviceConfigDto> queryDeviceConfig(EcosDeviceConfigQueryVo ecosDeviceConfigQueryVo) {
        return ecosServiceApi.queryDeviceConfig(ecosDeviceConfigQueryVo);
    }

    @Override
    public DataResponse<String> getAgentId(String deviceId) {
        return ecosServiceApi.getAgentId(deviceId);
    }

    @Override
    public List<BindInfoDTO> getAgentsByIds(List<Long> ids) {
        DataResponse<List<BindInfoDTO>> resp= ecosServiceApi.getAgentsByIds(ids);
        return parseBean(resp);
    }

    @Override
    public void speedupOnce(String deviceId) {
        ecosServiceApi.speedupOnce(deviceId);
    }

    @Override
    public List<HybridSinglePhaseDO> listOtherBindDevice(String wifiSn, String deviceName) {
        DataResponse<List<HybridSinglePhaseDO>> resp = ecosServiceApi.listOtherBindDevice(wifiSn, deviceName);
        return parseBean(resp);
    }

    @Override
    public void save(HybridSinglePhaseDO hybridSinglePhaseDO) {
        EmptyResponse emptyResponse = ecosServiceApi.saveNewDevice(hybridSinglePhaseDO);
        if (emptyResponse.getCode() != HttpStatus.OK.value()) {
            throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
        }
    }

    @Override
    public void updateById(HybridSinglePhaseDO hybridSinglePhaseDO) {
        EmptyResponse emptyResponse = ecosServiceApi.updateById(hybridSinglePhaseDO);
        if (emptyResponse.getCode() != HttpStatus.OK.value()) {
            throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
        }
    }

    @Override
    public void saveSocket(HybridSinglePhaseDO hybridSinglePhaseDO) {
        InResponse emptyResponse = ecosServiceApi.saveSocket(hybridSinglePhaseDO);
        if (emptyResponse.getCode() != HttpStatus.OK.value()) {
            throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
        }
    }

    @Override
    public void updSocket(HybridSinglePhaseDO hybridSinglePhaseDO) {
        InResponse emptyResponse = ecosServiceApi.updSocket(hybridSinglePhaseDO);
        if (emptyResponse.getCode() != HttpStatus.OK.value()) {
            throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
        }
    }

    @Override
    public void saveCharger(ChargerSaveVO chargerSaveVO) {
        InResponse emptyResponse = ecosServiceApi.saveCharger(chargerSaveVO);
        if (emptyResponse.getCode() != HttpStatus.OK.value()) {
            throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
        }
    }

    @Override
    public void updCharger(ChargerSaveVO chargerSaveVO) {
        InResponse emptyResponse = ecosServiceApi.updCharger(chargerSaveVO);
        if (emptyResponse.getCode() != HttpStatus.OK.value()) {
            throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
        }
    }

    @Override
    public List<HybridSinglePhaseDO> nowBindDeviceList(String wifiSn) {
        DataResponse<List<HybridSinglePhaseDO>> resp = ecosServiceApi.nowBindDeviceList(wifiSn);
        return parseBean(resp);
    }

    @Override
    public GlobalVersionDto ecosLatestVersion() {
        String language = LocaleUtil.getLanguage();
        DataResponse<GlobalVersionDto> resp = ecosServiceApi.ecosLatestVersion(language);
        return parseBean(resp);
    }

    @Override
    public GlobalEnestLatestVersionDto enestLatestVersion() {
        String language = LocaleUtil.getLanguage();
        DataResponse<GlobalEnestLatestVersionDto> resp = ecosServiceApi.enestLatestVersion(language);
        return parseBean(resp);
    }

    @Override
    public void updateSysTransTime(Long systemId, Integer saveDeviceTime) {
        EmptyResponse emptyResponse = ecosServiceApi.updSystemInfo(SystemInfoUpdVO.builder()
                .id(systemId)
                .transferTime(new Date())
                .currentStep(StepEnum.STEP_4.getStepNum())
                .saveDeviceTime(saveDeviceTime)
                .build());
        if (emptyResponse.getCode() != HttpStatus.OK.value()) {
            throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
        }
    }

    @Override
    public List<ResourceCategoryTreeDto> resourceCategoryTree() {
        DataResponse<List<ResourceCategoryTreeDto>> response = ecosServiceApi.resourceCategoryTree();
        return parseBean(response);
    }

    @Override
    public PageInfoDTO<V2ClientChargeRecordDto> pageChargeRecord(V2ClientChargeRecordPageVo v2ClientChargeRecordPageVo) {
        DataResponse<PageInfoDTO<V2ClientChargeRecordDto>> response = ecosServiceApi.pageChargeRecord(v2ClientChargeRecordPageVo);
        return parseBean(response);
    }

    @Override
    public V2ClientChargeRecordDto queryLastRecord(Long deviceId, Boolean isFilterCharing) {
        DataResponse<V2ClientChargeRecordDto> response = ecosServiceApi.queryLastRecord(deviceId, isFilterCharing);
        return parseBeanV2(response);
    }

    @Override
    public void saveChargeRecord(V2ClientChargeRecordSaveVo v2ClientChargeRecordSaveVo) {
        EmptyResponse emptyResponse = ecosServiceApi.saveChargeRecord(v2ClientChargeRecordSaveVo);
        if (emptyResponse.getCode() != HttpStatus.OK.value()) {
            throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
        }
    }

    @Override
    public void updChargeRecord(V2ClientChargeRecordUpdateVo v2ClientChargeRecordUpdateVo) {
        EmptyResponse emptyResponse = ecosServiceApi.updChargeRecord(v2ClientChargeRecordUpdateVo);
        if (emptyResponse.getCode() != HttpStatus.OK.value()) {
            throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
        }
    }

    @Override
    public void updChargerExtInfo(ExtInfoDto param) {
        InResponse inResponse = ecosServiceApi.updChargerExtInfo(param);
        if (inResponse.getCode() != HttpStatus.OK.value()) {
            throw new EcosException(EcosExceptionEnum.ASSERT_TRUE);
        }
    }

    @Override
    public void boundInstall(InstallBoundDTO item) {
        ecosServiceApi.boundInstall(item);
    }

    @Override
    public InstallBoundInfoDTO getBindInstallInfo(String deviceId) {
        DataResponse<InstallBoundInfoDTO> res = ecosServiceApi.getBindInstallInfo(deviceId);
        return parseBean(res);
    }

    @Override
    public DynamicExportDTO dynamicExport(String deviceName) {
        DataResponse<DynamicExportDTO> res = ecosServiceApi.dynamicExport(deviceName);
        return parseBeanV2(res);
    }

    @Override
    public DynamicDesignDTO designInfo(String deviceName) {
        DataResponse<DynamicDesignDTO> res = ecosServiceApi.designInfo(deviceName);
        return parseBeanV2(res);
    }

    @Override
    public void dynamicSave(DynamicSaveVO param) {
        ecosServiceApi.dynamicSave(param);
    }

    @Override
    public DynamicTestDTO dynamicTest(String deviceName) {
        DataResponse<DynamicTestDTO> res = ecosServiceApi.dynamicTest(deviceName);
        return parseBeanV2(res);
    }

    @Override
    public void dynamicSwitch(DynamicSwitchVO param) {
        ecosServiceApi.dynamicSwitch(param);
    }

    private <T> T parseBean(DataResponse<T> resp) {
        if (null == resp || HttpStatus.OK.value() != resp.getCode() || null == resp.getData()) {
            log.warn("HubService request failed, resp:{}", resp);
            throw new EcosException(EcosExceptionEnum.INVALID_DATA);
        }
        return resp.getData();
    }

    private <T> T parseBeanV2(DataResponse<T> resp) {
        if (HttpStatus.OK.value() != resp.getCode()) {
            log.warn("HubService request failed, resp:{}", resp);
            throw new EcosException(EcosExceptionEnum.INVALID_DATA);
        }
        return resp.getData();
    }
}
