package com.weihengtech.ecos.service.ele;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.consts.RedisRefConstants;
import com.weihengtech.ecos.enums.ele.ElePriceTypeEnum;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dos.ClientHomeDeviceDo;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dos.ClientHomeSaveCostDO;
import com.weihengtech.ecos.model.dtos.ele.ElePriceDetailDTO;
import com.weihengtech.ecos.service.app.impl.ClientCustomizeServiceImpl;
import com.weihengtech.ecos.service.app.impl.ClientHomeDeviceServiceImpl;
import com.weihengtech.ecos.utils.InitUtil;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface HomeElePriceService {

    /**
     * 查询电价配置信息统一入口
     *
     * @param homeId 家庭Id
     * @return 电价配置
     */
    Object queryEleInfoOrConfig(String homeId);

    /**
     * 计算15分钟电价统一入口：计算成本收益用
     *
     * @param homeId 家庭信息
     * @param time 时间：昨天：-1，今天：0
     * @return
     */
    Map<Long, ElePriceDetailDTO> query15HomeElePrice(String homeId, Integer time, String timezone);

    /**
     * 查询原始电价统一入口：计算策略用，目前仅支持批发电价、零售商电价
     *
     * @param homeId
     * @return
     */
    List<EleDayAheadPriceDto> queryOriginalElePrice(String homeId, Integer time, String timezone);

    /**
     * 查询指定电价类型的所有家庭电价信息
     *
     * @return
     */
    Object queryAllTypeHomePriceInfo(List<String> timezoneList);

    /**
     * 根据时区获取当日15分钟一个点的时间戳
     *
     * @param timezone 时区
     * @return
     */
    default List<Long> cal15MinDayTime(Integer time, String timezone) {
        List<Long> timestamps = new ArrayList<>();

        // 获取指定时区
        ZoneId zoneId = ZoneId.of(timezone);

        // 获取当前时区的当前日期和时间
        ZonedDateTime now = ZonedDateTime.now(zoneId);

        // 获取当天的开始时间（午夜）
        ZonedDateTime startOfDay = now.toLocalDate().plusDays(time).atStartOfDay(zoneId);

        // 生成从午夜开始每15分钟的时间点，直到下一天的午夜
        ZonedDateTime current = startOfDay;
        while (current.isBefore(startOfDay.plusDays(1))) {
            timestamps.add(current.toInstant().getEpochSecond());
            current = current.plusMinutes(15);
        }

        return timestamps;
    }

    /**
     * 切换电价类型时，需要关闭自动策略
     * @param homeItem 家庭信息
     */
    default void closeAutoStrategy(ClientHomeDo homeItem) {
        if (!ElePriceTypeEnum.WHOLESALE.getCode().equals(homeItem.getElePriceType()) &&
                !ElePriceTypeEnum.RETAIL.getCode().equals(homeItem.getElePriceType())) {
            return;
        }
        ClientHomeDeviceServiceImpl clientHomeDeviceService = InitUtil.getBean(ClientHomeDeviceServiceImpl.class);
        ClientCustomizeServiceImpl clientCustomizeService = InitUtil.getBean(ClientCustomizeServiceImpl.class);
        List<ClientHomeDeviceDo> deviceList = clientHomeDeviceService.list(Wrappers.<ClientHomeDeviceDo>lambdaQuery()
                .eq(ClientHomeDeviceDo::getHomeId, homeItem.getId()));
        if (CollUtil.isEmpty(deviceList)) {
            return;
        }
        List<Long> deviceIds = deviceList.stream().map(ClientHomeDeviceDo::getDeviceId).collect(Collectors.toList());
        List<ClientCustomizeDo> autoStrategyDevices = clientCustomizeService.getAutoStrategyDevices(deviceIds);
        if (CollUtil.isEmpty(autoStrategyDevices)) {
            return;
        }
        autoStrategyDevices.forEach(i -> {
            i.setStrategyMode(0);
            i.setAutoStrategy(0);
            i.setAutoHomeStrategy(0);
        });
        clientCustomizeService.updateBatchById(autoStrategyDevices);
    }

    /**
     * 清除指定家庭当日缓存成本数据
     *
     * @param homeId
     */
    default void clearCurDayCostData(Long homeId) {
        RedisTemplate<String, List<ClientHomeSaveCostDO>> bean = InitUtil.getBean("redisTemplate", RedisTemplate.class);
        bean.delete(RedisRefConstants.buildCostTodayKey(homeId));
    }

}
