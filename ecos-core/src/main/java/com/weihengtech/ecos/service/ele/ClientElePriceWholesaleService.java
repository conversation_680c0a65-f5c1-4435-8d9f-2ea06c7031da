package com.weihengtech.ecos.service.ele;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.vos.AheadPriceVo;
import com.weihengtech.ecos.model.dos.ClientElePriceWholesaleDO;
import com.weihengtech.ecos.model.dtos.ele.HomeElePriceDetailDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceWholesaleVO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface ClientElePriceWholesaleService extends IService<ClientElePriceWholesaleDO> {

    ClientElePriceWholesaleDO queryWholesalePrice(String homeId);

    HomeElePriceDetailDTO updateWholesalePrice(ElePriceWholesaleVO param);

    List<EleDayAheadPriceDto> getEleAheadPrice(AheadPriceVo aheadPriceVo);
}
