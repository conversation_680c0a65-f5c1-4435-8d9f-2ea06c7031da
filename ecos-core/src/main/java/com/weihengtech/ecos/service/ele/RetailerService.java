package com.weihengtech.ecos.service.ele;

import com.weihengtech.ecos.model.dos.ClientElePriceRetailerDO;
import com.weihengtech.ecos.model.dtos.ele.RetailerElePriceDTO;
import com.weihengtech.ecos.model.dtos.ele.TibberHomeDTO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface RetailerService {

    /**
     * 查询tibber家庭列表
     *
     * @param token
     * @return
     */
    List<TibberHomeDTO> queryTibberHomes(String token);

    /**
     * 查询指定零售商原始电价
     *
     * @param config
     * @param time
     * @param timezone
     * @return
     */
    RetailerElePriceDTO queryRetailerElePrice(ClientElePriceRetailerDO config, Integer time, String timezone);

}
