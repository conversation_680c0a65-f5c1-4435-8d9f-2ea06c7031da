package com.weihengtech.ecos.service.ele;

import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.vos.EleHomeStrategyPreviewVO;
import com.weihengtech.ecos.api.pojo.vos.EleStrategyPreviewVO;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoEzDto;
import com.weihengtech.ecos.model.dtos.ele.EleStrategyDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 日前电价策略服务
 *
 * <AUTHOR>
 * @date 2024/10/25 16:44
 * @version 1.0
 */
public interface ClientEleStrategyService {


    /**
     * 生成普通电价策略
     *
     * @param priceDataList 电价数据
     * @param param 策略入参
     * @return 电价策略
     */
    List<EleStrategyDTO> calStrategyWithNormal(List<EleDayAheadPriceDto> priceDataList, EleStrategyPreviewVO param);

    /**
     * 根据策略生成下发对象
     *
     * @param eleStrategyList 自动策略（可能包含家庭负载因素）
     * @param timezone 时区
     * @return 下发对象
     */
    CustomizeInfoEzDto queryStrategy(List<EleStrategyDTO> eleStrategyList, String timezone);

    /**
     * 根据家庭功率生成负载曲线
     *
     * @param param 条件
     * @return 负载曲线
     */
    Map<Integer, Integer> homePowerGraph(EleHomeStrategyPreviewVO param);

    /**
     * 在电价策略基础上加上家庭负载因素，生成最终策略
     *
     * @param eleStrategyRes 电价策略
     * @param eleHomePowerRes 家庭负载曲线
     */
    void calStrategyWithHomePower(List<EleStrategyDTO> eleStrategyRes, Map<Integer, Integer> eleHomePowerRes, String timezone);


    /**
     * 生成收益模式电价策略
     *
     * @param elePriceList 电价信息
     * @param param 参数
     * @return
     */
    List<EleStrategyDTO> calStrategyWithEarning(List<EleDayAheadPriceDto> elePriceList, EleStrategyPreviewVO param);

    /**
     * 生成负电价策略
     *
     * @param elePriceList 电价信息
     * @param param 参数
     * @return
     */
    List<EleStrategyDTO> calStrategyWithNegative(List<EleDayAheadPriceDto> elePriceList, EleStrategyPreviewVO param);
}
