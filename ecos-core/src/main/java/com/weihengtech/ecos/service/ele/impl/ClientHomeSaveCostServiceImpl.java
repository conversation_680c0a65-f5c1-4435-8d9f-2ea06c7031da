package com.weihengtech.ecos.service.ele.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.consts.RedisRefConstants;
import com.weihengtech.ecos.dao.ClientHomeSaveCostMapper;
import com.weihengtech.ecos.enums.SeriesEnum;
import com.weihengtech.ecos.model.dos.*;
import com.weihengtech.ecos.model.dtos.ele.HomeCostSavingDTO;
import com.weihengtech.ecos.model.dtos.ele.ElePriceDetailDTO;
import com.weihengtech.ecos.service.app.ClientHomeDeviceService;
import com.weihengtech.ecos.service.ele.ClientHomeSaveCostService;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.app.ClientHomeUserService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.TimeUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Service
public class ClientHomeSaveCostServiceImpl extends ServiceImpl<ClientHomeSaveCostMapper, ClientHomeSaveCostDO> implements ClientHomeSaveCostService {

    @Resource
    private ClientHomeService clientHomeService;
    @Resource
    private ClientHomeDeviceService clientHomeDeviceService;
    @Resource
    private HubService hubService;
    @Resource
    private RedisTemplate<String, List<ClientHomeSaveCostDO>> redisTemplate;


    @Override
    public HomeCostSavingDTO calHomeCostInfo(Long homeId) {
        List<ClientHomeDeviceDo> homeDeviceList = clientHomeDeviceService.getDevicesByHomeId(homeId);
        if (CollUtil.isEmpty(homeDeviceList)) {
            return HomeCostSavingDTO.builder().build();
        }
        String timezone = SecurityUtil.getClientUserDo().getTimeZone();
        Long dayStart = TimeUtil.getDayStart(0, timezone);
        Long dayEnd = TimeUtil.getDayEnd(0, timezone);
        // 读取今日缓存
        List<ClientHomeSaveCostDO> todayInfo = redisTemplate.opsForValue().get(RedisRefConstants.buildCostTodayKey(homeId));
        if (CollUtil.isNotEmpty(todayInfo)) {
            todayInfo.sort(Comparator.comparing(ClientHomeSaveCostDO::getHour));
            ClientHomeSaveCostDO latestOne = todayInfo.get(todayInfo.size() - 1);
            // 距离上次缓存时间超过一小时，则补充间隔时间的数据
            if (TimeUtil.calCurHour(DateUtil.currentSeconds(), timezone) - latestOne.getHour() >= 1) {
                long startTime = latestOne.getDay().toInstant()
                        .atZone(ZoneId.of(timezone))
                        .plusHours(latestOne.getHour())
                        .toEpochSecond();
                List<ClientHomeSaveCostDO> newData = costSaving(homeId, timezone, homeDeviceList,
                        startTime * 1000, System.currentTimeMillis());
                // 去掉上次缓存的最后一个钟的不完整数据，补充间隔的数据
                todayInfo.remove(todayInfo.size() - 1);
                todayInfo.addAll(newData);
                redisTemplate.opsForValue().set(RedisRefConstants.buildCostTodayKey(homeId), todayInfo,
                        dayEnd - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
            }
        } else {
            // 没有缓存则重新计算0点到此刻的数据并缓存
            todayInfo = costSaving(homeId, timezone, homeDeviceList,
                    dayStart, System.currentTimeMillis());
            redisTemplate.opsForValue().set(RedisRefConstants.buildCostTodayKey(homeId), todayInfo,
                    dayEnd - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
        }
        // 计算今日节省成本数据
        BigDecimal todayGrid = todayInfo.stream().map(ClientHomeSaveCostDO::getGridCost).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal todayLoad = todayInfo.stream().map(ClientHomeSaveCostDO::getLoadCost).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal todayFeed = todayInfo.stream().map(ClientHomeSaveCostDO::getFeedEarnings).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal todayEarning = todayLoad.subtract(todayGrid).add(todayFeed);
        // 计算周节省成本数据
        List<HomeCostSavingDTO.Detail> weekList = baseMapper.calDayCostData(homeId, Instant.ofEpochMilli(dayStart)
                .atZone(ZoneId.of(timezone))
                .minusDays(6)
                .toEpochSecond(), dayStart / 1000);
        // 补充周缺失数据
        weekList = replenishWeekData(weekList, dayStart, timezone);
        // 将今日数据补充到周
        weekList.add(HomeCostSavingDTO.Detail.builder()
                        .day(new Date(dayStart))
                        .loadCost(todayLoad)
                        .gridCost(todayGrid)
                        .feedEarnings(todayFeed).build());
        weekList.forEach(HomeCostSavingDTO.Detail::calSavings);
        // 计算月汇总数据
        HomeCostSavingDTO.Detail monthSum = baseMapper.sumCostData(homeId, Instant.ofEpochMilli(dayStart)
                .atZone(ZoneId.of(timezone))
                .minusDays(30)
                .toEpochSecond(), dayStart / 1000);
        BigDecimal monthEarning = Optional.ofNullable(monthSum)
                .map(i -> i.getLoadCost().subtract(i.getGridCost()).add(i.getFeedEarnings()))
                .orElse(BigDecimal.ZERO);
        return HomeCostSavingDTO.builder()
                .todaySavings(todayEarning)
                .weeklySavings(weekList)
                .monthLoadCost(Optional.ofNullable(monthSum).map(HomeCostSavingDTO.Detail::getLoadCost).orElse(BigDecimal.ZERO))
                .monthGridCost(Optional.ofNullable(monthSum).map(HomeCostSavingDTO.Detail::getGridCost).orElse(BigDecimal.ZERO))
                .monthFeedEarnings(Optional.ofNullable(monthSum).map(HomeCostSavingDTO.Detail::getFeedEarnings).orElse(BigDecimal.ZERO))
                .monthSavings(monthEarning)
                .build();
    }

    @Override
    public void clearHistoryCostInfo(Long homeId) {
        // 清除当日数据
        redisTemplate.delete(RedisRefConstants.buildCostTodayKey(homeId));
        // 清除历史数据
        remove(Wrappers.<ClientHomeSaveCostDO>lambdaQuery().eq(ClientHomeSaveCostDO::getHomeId, homeId));
    }

    /** 补充周缺失数据 */
    private List<HomeCostSavingDTO.Detail> replenishWeekData(List<HomeCostSavingDTO.Detail> weekList, Long dayStart, String timezone) {
        Map<Long, HomeCostSavingDTO.Detail> detailMap = weekList.stream()
                .collect(Collectors.toMap(i -> i.getDay().getTime(), Function.identity()));
        List<HomeCostSavingDTO.Detail> resList = new ArrayList<>();
        for (int i = 6; i > 0; i--) {
            long curDayStart = Instant.ofEpochMilli(dayStart)
                    .atZone(ZoneId.of(timezone))
                    .minusDays(i)
                    .toEpochSecond();
            HomeCostSavingDTO.Detail detail = detailMap.get(curDayStart * 1000);
            resList.add(detail == null ? HomeCostSavingDTO.Detail.builder().day(new Date(curDayStart * 1000)).build() : detail);
        }
        return resList;
    }


    @Override
    public List<ClientHomeSaveCostDO> costSaving(Long homeId, String userTimezone, List<ClientHomeDeviceDo> homeDeviceList, Long startTime, Long endTime) {
        ClientHomeDo homeInfo = clientHomeService.getById(homeId);
        Long dayStart = TimeUtil.getDayStart(0, userTimezone);
        int time = startTime >= dayStart ? 0 : -1;
        endTime = TimeUtil.calHourBegin(endTime, userTimezone);
        // 获取家庭电价
        Map<Long, ElePriceDetailDTO> priceMap = clientHomeService.calHomeElePrice(homeInfo, time, userTimezone);
        if (CollUtil.isEmpty(priceMap)) {
            return Collections.emptyList();
        }
        List<Long> deviceIdList = homeDeviceList.stream()
                .map(ClientHomeDeviceDo::getDeviceId).collect(Collectors.toList());
        LinkedHashMap<Long, ClientHomeSaveCostDO> homeMap = calCostEarningByPrice(deviceIdList, priceMap, userTimezone, startTime, endTime);
        return initHomeMap2List(homeInfo.getId(), startTime, userTimezone, homeMap);
    }

    @Override
    public List<ClientHomeSaveCostDO> costSaving(ClientHomeDo homeInfo, List<ClientHomeDeviceDo> homeDeviceList, Integer time) {
        String timezone = homeInfo.getTimezone();
        // 获取家庭电价
        Map<Long, ElePriceDetailDTO> priceMap;
        try {
            priceMap = clientHomeService.calHomeElePrice(homeInfo, time, timezone);
        } catch (Exception e) {
            log.error(String.format("home:%s calHomeElePrice error", homeInfo.getId()), e);
            priceMap = Collections.emptyMap();
        }
        if (CollUtil.isEmpty(priceMap)) {
            return Collections.emptyList();
        }
        // 查询起始、截止时间
        Long startTime = TimeUtil.getDayStart(time, timezone);
        Long endTime = TimeUtil.getDayEnd(time, timezone);
        List<Long> deviceIdList = homeDeviceList.stream()
                .map(ClientHomeDeviceDo::getDeviceId).collect(Collectors.toList());
        // 计算家庭成本、收益数据
        LinkedHashMap<Long, ClientHomeSaveCostDO> homeMap = calCostEarningByPrice(deviceIdList, priceMap, timezone, startTime, endTime);
        // 将数据转成持久化数据结构
        return initHomeMap2List(homeInfo.getId(), startTime, timezone, homeMap);
    }

    /**
     * 根据电价信息，获取家庭耗电量，计算成本、收益
     *
     * @param deviceIdList 家庭设备
     * @param priceMap 电价
     * @param userTimezone 时区
     * @param startTime 起始时间
     * @param endTime 截止时间
     * @return
     */
    private LinkedHashMap<Long, ClientHomeSaveCostDO> calCostEarningByPrice(
                                                             List<Long> deviceIdList,
                                                             Map<Long, ElePriceDetailDTO> priceMap,
                                                             String userTimezone,
                                                             Long startTime, Long endTime) {
        // 查询家庭设备信息 todo 单独家庭中查询设备信息，两方面考量：定时任务统一拉取效率高，但数据量大，可能内存溢出，先看下效果
        List<HybridSinglePhaseDO> deviceInfoList = hubService.getBatchById(false, deviceIdList);
        if (CollUtil.isEmpty(deviceInfoList)) {
            return new LinkedHashMap<>(1);
        }
        List<HybridSinglePhaseDO> storageDeviceList = deviceInfoList.stream()
                .filter(i -> SeriesEnum.isEnergyStorageDevice(i.getResourceSeriesId()))
                .collect(Collectors.toList());
        LinkedHashMap<Long, ClientHomeSaveCostDO> resMap = initHomeMap(startTime, endTime, userTimezone);
        for (HybridSinglePhaseDO device : storageDeviceList) {
            // 获取设备耗电量
            Map<String, LinkedHashMap<Long, Object>> costMap;
            try {
                costMap = clientHomeDeviceService.calCostBaseEleData(
                        device, startTime, endTime);
            } catch (Exception e) {
                log.error(String.format("device: %s calCostBaseEleData error", device.getDeviceSn()), e);
                costMap = Collections.emptyMap();
            }
            if (CollUtil.isEmpty(costMap)) {
                continue;
            }
            if (CollUtil.isEmpty(costMap.get(CommonConstants.LOAD_ELE)) ||
                    CollUtil.isEmpty(costMap.get(CommonConstants.GRID_ELE)) ||
                    CollUtil.isEmpty(costMap.get(CommonConstants.FEED_ELE))) {
                continue;
            }
            // 计算每小时负载消耗成本
            LinkedHashMap<Long, Object> loadCost = calCostEarning(userTimezone,
                    priceMap, costMap.get(CommonConstants.LOAD_ELE),
                    ElePriceDetailDTO::getSumPrice);
            // 计算每小时电网取电成本
            LinkedHashMap<Long, Object> gridCost = calCostEarning(userTimezone,
                    priceMap, costMap.get(CommonConstants.GRID_ELE),
                    ElePriceDetailDTO::getSumPrice);
            // 计算每小时馈网收益
            LinkedHashMap<Long, Object> feedEarning =calCostEarning(userTimezone,
                    priceMap, costMap.get(CommonConstants.FEED_ELE),
                    ElePriceDetailDTO :: getFeedInPrice);
            // 累计到家庭成本、收益数据中
            feedEarning.forEach((t, e) -> {
                ClientHomeSaveCostDO sumItem = resMap.get(t);
                sumItem.setGridCost(sumItem.getGridCost().add((BigDecimal) gridCost.get(t)));
                sumItem.setLoadCost(sumItem.getLoadCost().add((BigDecimal) loadCost.get(t)));
                sumItem.setFeedEarnings(sumItem.getFeedEarnings().add((BigDecimal) e));
            });
        }
        return resMap;
    }

    /** 家庭统计数据转成持久化数据结构 */
    private List<ClientHomeSaveCostDO> initHomeMap2List(Long homeId, Long startTime, String userTimezone,
                                                        LinkedHashMap<Long, ClientHomeSaveCostDO> initHomeMap) {
        if (CollUtil.isEmpty(initHomeMap)) {
            return Collections.emptyList();
        }
        List<ClientHomeSaveCostDO> resList = new ArrayList<>();
        initHomeMap.forEach((t, e) -> {
            resList.add(ClientHomeSaveCostDO.builder()
                    .homeId(homeId)
                    .day(new Date(startTime))
                    .hour(TimeUtil.calCurHour(t, userTimezone))
                    .loadCost(e.getLoadCost())
                    .gridCost(e.getGridCost())
                    .feedEarnings(e.getFeedEarnings())
                    .createTime(new Date())
                    .build());
        });
        return resList;
    }

    /** 初始化指定时间范围内每个小时的初始数据 */
    private LinkedHashMap<Long, ClientHomeSaveCostDO> initHomeMap(Long startTime, Long endTime, String userTimezone) {
        LinkedHashMap<Long, ClientHomeSaveCostDO> resMap = new LinkedHashMap<>();
        long startHour = TimeUtil.calHourBegin(startTime, userTimezone) / 1000;
        while (startHour * 1000 <= endTime) {
            resMap.put(startHour, ClientHomeSaveCostDO.builder()
                            .loadCost(new BigDecimal("0"))
                            .gridCost(new BigDecimal("0"))
                            .feedEarnings(new BigDecimal("0"))
                    .build());
            startHour += 3600;
        }
        return resMap;
    }

    /**
     * 计算消耗成本或馈网收益
     * 算法逻辑:
     * 1、以电量数据维度进行遍历（时间顺序）
     * 2、以有效电量数据段内的数据按照小时进行填充（15分钟数据累加）
     * 3、将每小时统计的数据映射到该小时起始时间戳
     *
     * @param priceMap 电价信息
     * @param eleMap 电量信息
     * @param function 取电价方法
     * @return
     */
    private LinkedHashMap<Long, Object> calCostEarning(String userTimezone,
                                                       Map<Long, ElePriceDetailDTO> priceMap,
                                                       LinkedHashMap<Long, Object> eleMap,
                                                       Function<ElePriceDetailDTO, BigDecimal> function) {
        LinkedHashMap<Long, Object> res = new LinkedHashMap<>();
        // 找出第一个时间内的截止小时点，确定时间范围
        Long start = eleMap.keySet().stream().findFirst().orElse(0L);
        Long hourEnd = TimeUtil.calHourBegin(start * 1000, 1, userTimezone) / 1000;
        // 统计小时内耗电量容器
        BigDecimal hourCost = new BigDecimal(0);
        // 索引，仅作为判断边界用
        int index = 1;
        for (Map.Entry<Long, Object> eleEntry : eleMap.entrySet()) {
            Long t = eleEntry.getKey();
            // 1、当前时间点的数据，如果超出了截止小时点，则清算前面累计的15分钟数据
            // 2、并重置容器
            // 3、将下一个截止小时点定为当前数据时间的那个小时（不能简单的累加一个小时，因为电量数据可能是断层的）
            if (t >= hourEnd) {
                Long hourStart = TimeUtil.calHourBegin(hourEnd * 1000, -1, userTimezone) / 1000;
                res.put(hourStart, hourCost);
                hourCost = new BigDecimal(0);
                hourEnd = TimeUtil.calHourBegin(t * 1000, 1, userTimezone) / 1000;
            }
            // 成本：总电价*电量 | 收益：馈网电价*电量
            // 小时内将结果进行累加到容器
            BigDecimal ele = new BigDecimal(eleEntry.getValue().toString());
            ElePriceDetailDTO price = priceMap.get(t);
            hourCost = hourCost.add(ele.multiply(function.apply(price)));
            // 如果遍历到边界，则将剩余数据进行映射即可
            if (index >= eleMap.size()) {
                Long hourStart = TimeUtil.calHourBegin(hourEnd * 1000, -1, userTimezone) / 1000;
                res.put(hourStart, hourCost);
                continue;
            }
            index ++;
        }
        return res;
    }
}
