package com.weihengtech.ecos.service.ele.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.dao.ClientElePriceRetailerMapper;
import com.weihengtech.ecos.enums.ele.ElePriceTypeDetailEnum;
import com.weihengtech.ecos.enums.ele.ElePriceTypeEnum;
import com.weihengtech.ecos.model.dos.ClientElePriceRetailerDO;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dtos.ele.ElePriceDetailDTO;
import com.weihengtech.ecos.model.dtos.ele.HomeElePriceDetailDTO;
import com.weihengtech.ecos.model.dtos.ele.RetailerElePriceDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceRetailerVO;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.ele.ClientElePriceRetailerService;
import com.weihengtech.ecos.service.ele.HomeElePriceService;
import com.weihengtech.ecos.service.global.StrategyService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
public class ClientElePriceRetailerServiceImpl extends ServiceImpl<ClientElePriceRetailerMapper, ClientElePriceRetailerDO> implements ClientElePriceRetailerService, HomeElePriceService {

    @Resource
    private ClientHomeService clientHomeService;
    @Resource
    private StrategyService strategyService;
    @Resource
    private RedisTemplate<String, List<EleDayAheadPriceDto>> redisTemplate;

    @Override
    public ClientElePriceRetailerDO queryRetailerPrice(String homeId) {
        return getOne(Wrappers.<ClientElePriceRetailerDO>lambdaQuery()
                .eq(ClientElePriceRetailerDO::getHomeId, homeId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HomeElePriceDetailDTO updateRetailerPrice(ElePriceRetailerVO param) {
        ClientElePriceRetailerDO exitsOne = queryRetailerPrice(param.getHomeId());
        if (exitsOne == null) {
            save(ClientElePriceRetailerDO.builder()
                    .homeId(Long.parseLong(param.getHomeId()))
                    .token(param.getToken())
                    .retailer(param.getRetailer())
                    .retailerHomeId(param.getRetailerHomeId())
                    .account(param.getAccount())
                    .build());
        } else {
            BeanUtils.copyProperties(param, exitsOne);
            updateById(exitsOne);
        }
        // 更新家庭电价类型
        ClientHomeDo homeItem = clientHomeService.getById(param.getHomeId());
        if (homeItem != null && !ElePriceTypeEnum.RETAIL.getCode().equals(homeItem.getElePriceType())) {
            homeItem.setElePriceType(ElePriceTypeEnum.RETAIL.getCode());
            // Todo 暂时默认为1h
            //  还需要根据零售商电价类型，设置电价细分类型
            homeItem.setElePriceDetailType(ElePriceTypeDetailEnum.INTERVAL_1H.getCode());

            clientHomeService.updateById(homeItem);
        }
        // 更新家庭币种
        if (homeItem != null && param.getCurrency() != null) {
            homeItem.setCurrency(param.getCurrency());
            clientHomeService.updateById(homeItem);
        }
        // 清除当日成本缓存数据
        clearCurDayCostData(Long.parseLong(param.getHomeId()));

        return HomeElePriceDetailDTO.builder()
                .elePriceType(ElePriceTypeEnum.RETAIL.getCode())
                .elePriceDetailType(ElePriceTypeDetailEnum.INTERVAL_1H.getCode())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRetailerPrice(String homeId) {
        remove(Wrappers.<ClientElePriceRetailerDO>lambdaQuery()
                .eq(ClientElePriceRetailerDO::getHomeId, homeId));
        // 更新家庭电价类型
        ClientHomeDo homeItem = clientHomeService.getById(homeId);
        if (homeItem != null && ElePriceTypeEnum.RETAIL.getCode().equals(homeItem.getElePriceType())) {
            clientHomeService.update(Wrappers.<ClientHomeDo>lambdaUpdate()
                    .set(ClientHomeDo::getElePriceType, null)
                    .set(ClientHomeDo::getElePriceDetailType, null)
                    .eq(ClientHomeDo::getId, homeItem.getId()));
        }


    }

    @Override
    public List<ClientElePriceRetailerDO> queryAllTypeHomePriceInfo(List<String> timezoneList) {
        List<ClientHomeDo> homeList = clientHomeService.list(Wrappers.<ClientHomeDo>lambdaQuery()
                .eq(ClientHomeDo::getElePriceType, ElePriceTypeEnum.RETAIL.getCode()));
        if (CollUtil.isEmpty(homeList)) {
            return Collections.emptyList();
        }
        List<Long> homeIdList = homeList.stream().map(ClientHomeDo::getId).collect(Collectors.toList());
        return list(Wrappers.<ClientElePriceRetailerDO>lambdaQuery()
                .in(ClientElePriceRetailerDO::getHomeId, homeIdList));
    }

    @Override
    public Object queryEleInfoOrConfig(String homeId) {
        return queryRetailerPrice(homeId);
    }

    @Override
    public List<EleDayAheadPriceDto> queryOriginalElePrice(String homeId, Integer time, String timezone) {
        ClientElePriceRetailerDO config = queryRetailerPrice(homeId);
        RetailerElePriceDTO res = strategyService.chooseRetailerService(config.getRetailer())
                .queryRetailerElePrice(config, time, timezone);
        return res.getPriceList();
    }

    @Override
    public Map<Long, ElePriceDetailDTO> query15HomeElePrice(String homeId, Integer time, String userTimezone) {
        List<EleDayAheadPriceDto> priceList;
        if (time == -1) {
            priceList = redisTemplate.opsForValue().get(homeId);
        } else if (time == 0) {
            priceList = queryOriginalElePrice(homeId, time, userTimezone);
        } else {
            return Collections.emptyMap();
        }
        if (CollUtil.isEmpty(priceList)) {
            return Collections.emptyMap();
        }
        if (priceList.size() == 96) {
            return priceList.stream().collect(Collectors.toMap(EleDayAheadPriceDto::getStartTimeUnix,
                    i -> ElePriceDetailDTO.builder()
                            .purchasePrice(i.getAverage())
                            .purchaseTax(i.getTax())
                            .feedInPrice(i.getAverage())
                            .build()));
        }
        Map<Long, ElePriceDetailDTO> resMap = new HashMap<>();
        for (EleDayAheadPriceDto hourPrice : priceList) {
            resMap.put(hourPrice.getStartTimeUnix(), ElePriceDetailDTO.builder()
                    .purchasePrice(hourPrice.getAverage())
                    .purchaseTax(hourPrice.getTax())
                    .feedInPrice(hourPrice.getAverage())
                    .build());
            resMap.put(hourPrice.getStartTimeUnix() + 60 * 15, ElePriceDetailDTO.builder()
                    .purchasePrice(hourPrice.getAverage())
                    .purchaseTax(hourPrice.getTax())
                    .feedInPrice(hourPrice.getAverage())
                    .build());
            resMap.put(hourPrice.getStartTimeUnix() + 60 * 30, ElePriceDetailDTO.builder()
                    .purchasePrice(hourPrice.getAverage())
                    .purchaseTax(hourPrice.getTax())
                    .feedInPrice(hourPrice.getAverage())
                    .build());
            resMap.put(hourPrice.getStartTimeUnix() + 60 * 45, ElePriceDetailDTO.builder()
                    .purchasePrice(hourPrice.getAverage())
                    .purchaseTax(hourPrice.getTax())
                    .feedInPrice(hourPrice.getAverage())
                    .build());
        }
        return resMap;
    }
}
