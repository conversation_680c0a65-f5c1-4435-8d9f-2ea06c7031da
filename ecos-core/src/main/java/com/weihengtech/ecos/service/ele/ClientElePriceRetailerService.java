package com.weihengtech.ecos.service.ele;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.ecos.model.dos.ClientElePriceRetailerDO;
import com.weihengtech.ecos.model.dtos.ele.HomeElePriceDetailDTO;
import com.weihengtech.ecos.model.vos.price.ElePriceRetailerVO;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface ClientElePriceRetailerService extends IService<ClientElePriceRetailerDO> {

    ClientElePriceRetailerDO queryRetailerPrice(String homeId);

    HomeElePriceDetailDTO updateRetailerPrice(ElePriceRetailerVO param);

    void deleteRetailerPrice(String homeId);
}
