package com.weihengtech.ecos.service.app.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.ecos.dao.ClientHomeMapper;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dtos.ele.ElePriceDetailDTO;
import com.weihengtech.ecos.model.dtos.ele.HomeElePriceDTO;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.ele.HomeElePriceService;
import com.weihengtech.ecos.service.global.StrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @program: ecos-server
 * @description: 家庭表服务实现类
 * @author: jiahao.jin
 * @create: 2024-01-21 11:25
 **/
@Service
@RequiredArgsConstructor
public class ClientHomeServiceImpl extends ServiceImpl<ClientHomeMapper, ClientHomeDo> implements ClientHomeService {

    @Resource
    private StrategyService strategyService;

    @Override
    public HomeElePriceDTO familyPriceInfo(String homeId) {
        ClientHomeDo homeInfo = getById(homeId);
        if (homeInfo.getElePriceType() == null || homeInfo.getElePriceDetailType() == null) {
            return HomeElePriceDTO.builder().build();
        }
        HomeElePriceService homeElePriceService = strategyService.chooseHomeElePriceService(homeInfo.getElePriceType());
        Object priceInfo = homeElePriceService.queryEleInfoOrConfig(homeId);
        return HomeElePriceDTO.builder()
                .elePriceType(homeInfo.getElePriceType())
                .elePriceDetailType(homeInfo.getElePriceDetailType())
                .priceInfo(priceInfo)
                .build();
    }

    @Override
    public Map<Long, ElePriceDetailDTO> calHomeElePrice(ClientHomeDo homeInfo, Integer time, String userTimezone) {
        HomeElePriceService homeElePriceService = strategyService.chooseHomeElePriceService(homeInfo.getElePriceType());
        if (homeElePriceService == null) {
            return Collections.emptyMap();
        }
        return homeElePriceService.query15HomeElePrice(String.valueOf(homeInfo.getId()), time, userTimezone);
    }

    @Override
    public List<ClientHomeDo> queryHomePriceHomeList(List<String> timezoneList) {
        return baseMapper.queryHomePriceHomeList(timezoneList);
    }
}
