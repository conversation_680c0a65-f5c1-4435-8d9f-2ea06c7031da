package com.weihengtech.ecos.service.ele.impl.retailer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.enums.ele.ElePriceTypeDetailEnum;
import com.weihengtech.ecos.model.dos.ClientElePriceRetailerDO;
import com.weihengtech.ecos.model.dtos.ele.OctopusElePriceDTO;
import com.weihengtech.ecos.model.dtos.ele.RetailerElePriceDTO;
import com.weihengtech.ecos.model.dtos.ele.TibberHomeDTO;
import com.weihengtech.ecos.model.vos.price.OctopusAuthVO;
import com.weihengtech.ecos.model.vos.price.OctopusEleVO;
import com.weihengtech.ecos.service.ele.RetailerService;
import com.weihengtech.ecos.utils.ElectricityPriceTypeUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/22 14:28
 */
@Service
@Slf4j
public class OctopusServiceImpl implements RetailerService {

    @Value("${custom.url.octopus:https://api.octopus.energy/v1/graphql/}")
    private String octopusUrl;

    @Resource
    private OkHttpClient okHttpClient;

    /**
     * 查询Tibber家庭信息
     * @param token 认证令牌
     * @return 空列表（Octopus不使用此方法）
     */
    @Override
    public List<TibberHomeDTO> queryTibberHomes(String token) {
        return Collections.emptyList();
    }


    /**
     * 查询Octopus零售商电价信息
     * @param config 电价配置信息
     * @param time 时间参数（0表示当天，1表示明天）
     * @param timezone 时区
     * @return 电价信息列表
     */
    @Override
    public RetailerElePriceDTO queryRetailerElePrice(ClientElePriceRetailerDO config, Integer time, String timezone) {
        // 步骤1：获取Octopus认证token
        String token = getOctopusToken(config.getToken());
        if (StrUtil.isBlank(token)) {
            log.error("octopus token is empty");
            return RetailerElePriceDTO.builder().build();
        }
        try {
            // 步骤2：构建价格查询请求
            Request request = buildPriceRequest(token, config.getAccount());
            // 步骤3：获取并处理价格响应数据
            List<OctopusElePriceDTO> octopusPriceList = fetchAndProcessPriceResponse(request);

            // 步骤4：检查是否获取到价格数据
            if (CollUtil.isEmpty(octopusPriceList)) {
                return RetailerElePriceDTO.builder().build();
            }
            // 步骤5：检查电价类型
            ElePriceTypeDetailEnum elePriceTypeDetailEnum = parserPriceType(octopusPriceList);

            // 步骤5：处理价格数据并返回
            List<EleDayAheadPriceDto> priceList = processPriceData(octopusPriceList, time, timezone);
            return RetailerElePriceDTO.builder()
                    .elePriceDetailType(elePriceTypeDetailEnum.getCode())
                    .priceList(priceList)
                    .build();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return RetailerElePriceDTO.builder().build();
        }
    }

    /**
     * 检查电价类型
     * @param octopusPriceList Octopus电价数据列表
     * @return 电价类型
     */
    private ElePriceTypeDetailEnum parserPriceType(List<OctopusElePriceDTO> octopusPriceList) {
        octopusPriceList.sort(Comparator.comparing(OctopusElePriceDTO::getValidFrom));
        List<EleDayAheadPriceDto> elePriceList = octopusPriceList.stream()
                .map(i -> EleDayAheadPriceDto.builder()
                        .average(i.getValue())
                        .startTimeUnix(ZonedDateTime.parse(i.getValidFrom()).toInstant().getEpochSecond())
                        .build())
                .collect(Collectors.toList());
        return ElectricityPriceTypeUtil.detectPriceType(elePriceList);
    }

    /**
     * 构建价格查询请求
     * @param token 认证token
     * @param accountNumber 账户号码
     * @return HTTP请求对象
     */
    private Request buildPriceRequest(String token, String accountNumber) {
        // 步骤1：构建请求变量
        OctopusEleVO.Variable variable = OctopusEleVO.Variable.builder()
                .accountNumber(accountNumber)
                .build();
        // 步骤2：构建请求VO对象
        OctopusEleVO eleVO = new OctopusEleVO();
        eleVO.setVariables(variable);
        
        // 步骤3：创建请求体
        RequestBody body = RequestBody.create(JSONUtil.toJsonStr(eleVO),
                MediaType.parse("application/json; charset=utf-8"));
        
        // 步骤4：构建HTTP请求
        return new Request.Builder()
                .header("Authorization", token)
                .url(octopusUrl)
                .post(body)
                .build();
    }
    
    /**
     * 获取并处理价格响应数据<octopus的电价类型，电价数据></>
     * @param request HTTP请求对象
     * @return Octopus电价数据列表
     * @throws IOException IO异常
     */
    private List<OctopusElePriceDTO> fetchAndProcessPriceResponse(Request request) throws IOException {
        // 步骤1：发送HTTP请求
        try (Response response = okHttpClient.newCall(request).execute()) {
            // 步骤2：检查响应是否成功
            if (!response.isSuccessful() || response.body() == null) {
                log.error("The request for the octopus ele list failed");
                return Collections.emptyList();
            }
            
            // 步骤3：解析响应JSON数据
            JSONObject resJson = JSONUtil.parseObj(response.body().string());
            // 步骤4：检查是否有错误信息
            if (resJson.containsKey("errors")) {
                Optional.ofNullable(resJson.getJSONArray("errors"))
                        .map(i -> i.get(0))
                        .map(i -> ((JSONObject) i).getStr("message"))
                        .ifPresent(log::error);
                return Collections.emptyList();
            }

            // 步骤5：从响应中提取电价数据
            return Optional.ofNullable(resJson.getJSONObject("data"))
                    .map(i -> i.getJSONObject("account"))
                    .map(i -> i.getJSONArray("properties"))
                    .map(i -> i.getJSONObject(0))
                    .map(i -> i.getJSONArray("electricityMeterPoints"))
                    .map(i -> i.getJSONObject(0))
                    .map(i -> i.getJSONArray("agreements"))
                    .map(i -> i.getJSONObject(0))
                    .map(i -> i.getJSONObject("tariff"))
                    .map(i -> i.getJSONArray("unitRates"))
                    .map(i -> i.toList(OctopusElePriceDTO.class))
                    .orElse(Collections.emptyList());
        }
    }
    
    /**
     * 处理价格数据
     * @param octopusPriceList Octopus电价数据列表
     * @param time 时间参数
     * @param timezone 时区
     * @return 处理后的电价数据列表
     */
    private List<EleDayAheadPriceDto> processPriceData(List<OctopusElePriceDTO> octopusPriceList, Integer time, String timezone) {
        // 步骤2：检查时间间隔类型
        String validTo = octopusPriceList.get(0).getValidTo();
        long epochSecond = ZonedDateTime.parse(validTo).toEpochSecond();
        TimeIntervalType timeIntervalType = getTimeIntervalType(epochSecond);
        // 步骤3：计算时间范围
        TimeRange timeRange = calculateTimeRange(time, timezone);
        
        // 步骤4：按照时间间隔处理每段数据
        return processHourlyPrices(octopusPriceList, timeRange, timeIntervalType);
    }
    
    /**
     * 计算时间范围
     * @param time 时间参数（0表示当天，1表示明天）
     * @param timezone 时区
     * @return 时间范围对象
     */
    private TimeRange calculateTimeRange(Integer time, String timezone) {
        // 步骤1：获取当前时间
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(timezone));
        ZonedDateTime targetStart;
        ZonedDateTime targetEnd;
        
        // 步骤2：根据时间参数确定目标时间范围
        if (time == 0) {
            // 当天时间范围
            targetStart = now.truncatedTo(ChronoUnit.DAYS);
            targetEnd = targetStart.plusDays(1);
        } else {
            // 明天时间范围
            targetStart = now.truncatedTo(ChronoUnit.DAYS).plusDays(1);
            targetEnd = targetStart.plusDays(1);
        }
        
        // 步骤3：返回时间范围对象
        return new TimeRange(targetStart.toEpochSecond(), targetEnd.toEpochSecond());
    }
    
    /**
     * 处理每小时价格数据
     * @param octopusPriceList Octopus电价数据列表
     * @param timeRange 时间范围
     * @return 每小时电价数据列表
     */
    private List<EleDayAheadPriceDto> processHourlyPrices(List<OctopusElePriceDTO> octopusPriceList, TimeRange timeRange,
                                                          TimeIntervalType timeIntervalType) {
        List<EleDayAheadPriceDto> timePriceList = new ArrayList<>();
        
        // 步骤1：遍历所有价格数据
        for (OctopusElePriceDTO price : octopusPriceList) {
            // 步骤2：解析价格生效时间
            ZonedDateTime validFrom = ZonedDateTime.parse(price.getValidFrom());
            ZonedDateTime validTo = ZonedDateTime.parse(price.getValidTo());
            
            // 步骤3：转换为时间戳
            long fromTimestamp = validFrom.toEpochSecond();
            long toTimestamp = validTo.toEpochSecond();
            
            // 步骤4：检查是否在目标时间范围内
            if (toTimestamp <= timeRange.startTimestamp || fromTimestamp >= timeRange.endTimestamp) {
                continue;
            }

            // 步骤5：计算调整后的时间范围
            long adjustedFrom = Math.max(fromTimestamp, timeRange.startTimestamp);
            long adjustedTo = Math.min(toTimestamp, timeRange.endTimestamp);
            
            // 步骤6：生成每小时价格数据
            int intervalSeconds;
            switch (timeIntervalType) {
                case HOUR:
                    intervalSeconds = 3600;
                    break;
                case HALF_HOUR:
                    intervalSeconds = 1800;
                    break;
                case QUARTER_HOUR:
                    intervalSeconds = 900;
                    break;
                default:
                    intervalSeconds = 3600;
                    break;
            }
            timePriceList.addAll(generateHourlyPrices(price, adjustedFrom, adjustedTo, intervalSeconds));
        }
        
        return timePriceList;
    }
    
    /**
     * 生成每小时价格数据
     * @param price Octopus电价数据
     * @param adjustedFrom 调整后的开始时间
     * @param adjustedTo 调整后的结束时间
     * @return 每小时电价数据列表
     */
    private List<EleDayAheadPriceDto> generateHourlyPrices(OctopusElePriceDTO price, long adjustedFrom, long adjustedTo,
                                                           int intervalSeconds) {
        List<EleDayAheadPriceDto> hourlyPrices = new ArrayList<>();
        long currentHour = adjustedFrom;
        
        // 步骤1：按小时生成价格数据
        while (currentHour < adjustedTo) {
            // 步骤2：计算下一小时时间
            long nextHour = currentHour + intervalSeconds;
            long hourEnd = Math.min(nextHour, adjustedTo);
            
            // 步骤3：创建每小时价格对象
            EleDayAheadPriceDto dto = new EleDayAheadPriceDto();
            dto.setStartTimeUnix(currentHour);
            dto.setAverage(price.getValue());
            hourlyPrices.add(dto);
            
            // 步骤4：移动到下一小时
            currentHour = hourEnd;
        }
        
        return hourlyPrices;
    }
    
    /**
     * 时间范围内部类
     */
    private static class TimeRange {
        final long startTimestamp;
        final long endTimestamp;
        
        /**
         * 构造函数
         * @param startTimestamp 开始时间戳
         * @param endTimestamp 结束时间戳
         */
        TimeRange(long startTimestamp, long endTimestamp) {
            this.startTimestamp = startTimestamp;
            this.endTimestamp = endTimestamp;
        }
    }

    /**
     * 判断给定时间戳是否为整点时间
     * @param timestamp 时间戳（秒级）
     * @return 时间间隔类型：HOUR（小时整点）、HALF_HOUR（30分钟整点）、QUARTER_HOUR（15分钟整点）、NONE（非整点）
     */
    public TimeIntervalType getTimeIntervalType(long timestamp) {
        // 步骤1：将时间戳转换为Instant对象
        Instant instant = Instant.ofEpochSecond(timestamp);
        
        // 步骤2：转换为ZonedDateTime以获取分钟和秒数
        ZonedDateTime dateTime = instant.atZone(ZoneId.of("UTC"));
        
        // 步骤3：获取分钟和秒数
        int minute = dateTime.getMinute();
        int second = dateTime.getSecond();
        
        // 步骤4：检查是否为整点（分钟和秒都为0）
        if (minute == 0 && second == 0) {
            return TimeIntervalType.HOUR;
        }
        
        // 步骤5：检查是否为30分钟整点（分钟为30，秒为0）
        if (minute == 30 && second == 0) {
            return TimeIntervalType.HALF_HOUR;
        }
        
        // 步骤6：检查是否为15分钟整点（分钟为0、15、30、45，秒为0）
        if ((minute == 0 || minute == 15 || minute == 30 || minute == 45) && second == 0) {
            return TimeIntervalType.QUARTER_HOUR;
        }
        
        // 步骤7：如果不是任何整点时间
        return TimeIntervalType.NONE;
    }

    /**
     * 时间间隔类型枚举
     */
    public enum TimeIntervalType {
        HOUR,           // 小时整点
        HALF_HOUR,      // 30分钟整点
        QUARTER_HOUR,   // 15分钟整点
        NONE           // 非整点
    }

    /**
     * 获取Octopus认证token
     * @param apiKey API密钥
     * @return 认证token
     */
    private String getOctopusToken(String apiKey) {
        // 步骤1：构建认证请求的输入参数
        OctopusAuthVO.Variable.Input input = OctopusAuthVO.Variable.Input.builder().APIKey(apiKey).build();
        OctopusAuthVO.Variable variable = OctopusAuthVO.Variable.builder().input(input).build();
        OctopusAuthVO authVO = new OctopusAuthVO();
        authVO.setVariables(variable);
        
        // 步骤2：构建请求体
        RequestBody body = RequestBody.create(JSONUtil.toJsonStr(authVO),
                MediaType.parse("application/json; charset=utf-8"));
        
        // 步骤3：构建HTTP请求
        Request request = new Request.Builder()
                .url(octopusUrl)
                .post(body)
                .build();
        
        // 步骤4：发送请求并处理响应
        try (Response response = okHttpClient.newCall(request).execute()) {
            // 步骤5：检查响应是否成功
            if (!response.isSuccessful() || response.body() == null) {
                log.error("The request for the octopus token failed");
                return null;
            }
            
            // 步骤6：解析响应JSON数据
            JSONObject resJson = JSONUtil.parseObj(response.body().string());
            // 步骤7：检查是否有错误信息
            if (resJson.containsKey("errors")) {
                Optional.ofNullable(resJson.getJSONArray("errors"))
                        .map(i -> i.get(0))
                        .map(i -> ((JSONObject) i).getStr("message"))
                        .ifPresent(log::error);
                return null;
            }
            
            // 步骤8：提取并返回token
            return Optional.ofNullable(resJson.getJSONObject("data"))
                    .map(i -> i.getJSONObject("obtainKrakenToken"))
                    .map(i -> i.getStr("token"))
                    .orElse(null);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }
}