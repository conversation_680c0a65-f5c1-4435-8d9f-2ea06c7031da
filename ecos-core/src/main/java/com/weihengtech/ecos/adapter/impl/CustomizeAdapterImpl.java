package com.weihengtech.ecos.adapter.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.ecos.adapter.CustomizeAdapter;
import com.weihengtech.ecos.adapter.V2HomeAdapter;
import com.weihengtech.ecos.api.EcosEleApi;
import com.weihengtech.ecos.api.EcosIotApi;
import com.weihengtech.ecos.api.OssGlobalConfigApi;
import com.weihengtech.ecos.api.pojo.dtos.EleCountryRegionDto;
import com.weihengtech.ecos.api.pojo.dtos.EleDataSourceDto;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.dtos.EleTimeZoneDto;
import com.weihengtech.ecos.api.pojo.vos.AheadPriceVo;
import com.weihengtech.ecos.api.pojo.vos.EleHomeStrategyPreviewVO;
import com.weihengtech.ecos.api.pojo.vos.EleStrategyPreviewVO;
import com.weihengtech.ecos.common.DataResponse;
import com.weihengtech.ecos.common.InResponse;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.common.exception.UnauthorizedException;
import com.weihengtech.ecos.enums.DeviceStatusEnum;
import com.weihengtech.ecos.enums.ele.PriceImportTypeEnum;
import com.weihengtech.ecos.enums.ele.StrategyModeEnum;
import com.weihengtech.ecos.model.bos.global.OssGlobalConfigBo;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import com.weihengtech.ecos.model.dos.ClientUserDo;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dos.MiddleClientUserDeviceDo;
import com.weihengtech.ecos.model.dtos.app.RippleControlDTO;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoDto;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoEzDto;
import com.weihengtech.ecos.model.dtos.customize.TimeListLastDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicDesignDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicExportDTO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSaveVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicSwitchVO;
import com.weihengtech.ecos.model.dtos.dynamic.DynamicTestDTO;
import com.weihengtech.ecos.model.dtos.ele.EleRegionDTO;
import com.weihengtech.ecos.model.dtos.ele.EleStrategyDTO;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzV2Vo;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoEzVo;
import com.weihengtech.ecos.model.vos.customize.CustomizeInfoVo;
import com.weihengtech.ecos.service.app.ClientCustomizeLastService;
import com.weihengtech.ecos.service.app.ClientCustomizeService;
import com.weihengtech.ecos.service.app.ClientHomeService;
import com.weihengtech.ecos.service.app.MiddleClientUserDeviceService;
import com.weihengtech.ecos.service.ele.ClientElePriceWholesaleService;
import com.weihengtech.ecos.service.ele.ClientEleStrategyService;
import com.weihengtech.ecos.service.ele.HomeElePriceService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.ActionFlagUtil;
import com.weihengtech.ecos.utils.NumberUtil;
import com.weihengtech.ecos.utils.OperationUtil;
import com.weihengtech.ecos.utils.SecurityUtil;
import com.weihengtech.ecos.utils.SnowFlakeUtil;
import com.weihengtech.ecos.utils.TimezoneUtil;
import com.weihengtech.ecos.utils.TransformUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomizeAdapterImpl implements CustomizeAdapter {

	private static final String MULTI_CHARGE_FLAG = "config:battery_setting:loading_shifting:MultistageChargeAndDischarge";
	private static final String ABANDON_PV_FLAG = "config:battery_setting:loading_shifting:abandonPV";

	@Resource
	private MiddleClientUserDeviceService middleClientUserDeviceService;
	@Resource
	private ClientCustomizeService clientCustomizeService;
	@Resource
	private SnowFlakeUtil snowFlakeUtil;
	@Resource
	private HubService hubService;
	@Resource
	private EcosEleApi ecosEleApi;
	@Resource
	private EcosIotApi ecosIotApi;
	@Resource
	private V2HomeAdapter v2HomeAdapter;
	@Resource
	private ClientEleStrategyService clientEleStrategyService;
	@Resource
	private OssGlobalConfigApi ossGlobalConfigApi;
	@Resource
	private ClientElePriceWholesaleService clientElePriceWholesaleService;
	@Resource
	private ClientCustomizeLastService clientCustomizeLastService;
	@Resource
	private StrategyService strategyService;
	@Resource
	private ClientHomeService clientHomeService;

	@Override
	public CustomizeInfoDto readCustomize(String deviceId) {
		Pair<String, HybridSinglePhaseDO> pair = authenticateDevice(deviceId);
		CustomizeInfoDto customizeInfoDto = new CustomizeInfoDto();
		OperationUtil
				.of(clientCustomizeService
						.getOne(Wrappers.<ClientCustomizeDo>lambdaQuery().eq(ClientCustomizeDo::getDeviceId, deviceId)))
				.ifPresentOrElse(
						clientCustomizeDo -> CustomizeInfoDto.queryDataFromDb(clientCustomizeDo, customizeInfoDto),
						() -> this.queryFromDevice(deviceId, pair.getValue(), customizeInfoDto)
				);
		TimezoneUtil.convertGMTToUserTimezone(customizeInfoDto, pair.getKey());
		TimezoneUtil.convertGmtToUserTimezone(customizeInfoDto.getChargingList(), pair.getKey());
		TimezoneUtil.convertGmtToUserTimezone(customizeInfoDto.getDischargingList(), pair.getKey());
		HybridSinglePhaseDO deviceInfo = hubService.getById(Long.parseLong(deviceId));
		customizeInfoDto.setEmsSoftwareVersion(deviceInfo.getEmsSoftwareVersion());
		customizeInfoDto.setDsp1SoftwareVersion(deviceInfo.getDsp1SoftwareVersion());
		customizeInfoDto.setRatedPower(deviceInfo.getRatedPower());
		customizeInfoDto.setDefChargePower(Optional.ofNullable(customizeInfoDto.getDefChargePower())
				.orElse(Integer.parseInt(deviceInfo.getRatedPower().substring(0, deviceInfo.getRatedPower().length()-1)) / 2));
		customizeInfoDto.setDefDischargePower(Optional.ofNullable(customizeInfoDto.getDefDischargePower())
				.orElse(Integer.parseInt(deviceInfo.getRatedPower().substring(0, deviceInfo.getRatedPower().length()-1))));
		return customizeInfoDto;
	}

	@DSTransactional
	private void queryFromDevice(String deviceId, HybridSinglePhaseDO hybridSinglePhaseDO, CustomizeInfoDto customizeInfoDto) {
		Integer sysRunMode = ecosIotApi.getDeviceStatus(hybridSinglePhaseDO);

		if (sysRunMode.equals(DeviceStatusEnum.OFFLINE.getDbCode())) {
			log.info("设备id： " + deviceId + " 离线");
			return;
		}
		String cloud = String.valueOf(hybridSinglePhaseDO.getDataSource());
		List<Integer> valList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 41001, 27, cloud);
		if (CollUtil.isEmpty(valList)) {
			log.warn("读取设备长度为0");
			throw new EcosException(EcosExceptionEnum.READ_DEVICE_CONFIG_ERROR);
		}

		setCustomizeInfoDto(customizeInfoDto, valList);

		ClientCustomizeDo clientCustomizeDo = new ClientCustomizeDo();
		clientCustomizeDo.setId(snowFlakeUtil.generateId());
		clientCustomizeDo.setDeviceId(Long.parseLong(deviceId));

		clientCustomizeDo.setBatteryMin(valList.get(1));
		clientCustomizeDo.setChargeMode(valList.get(2));
		clientCustomizeDo.setChargeStartHour1(valList.get(7));
		clientCustomizeDo.setChargeStartMinute1(valList.get(8));
		clientCustomizeDo.setChargeEndHour1(valList.get(9));
		clientCustomizeDo.setChargeEndMinute1(valList.get(10));

		clientCustomizeDo.setDischargeStartHour1(valList.get(12));
		clientCustomizeDo.setDischargeStartMinute1(valList.get(13));
		clientCustomizeDo.setDischargeEndHour1(valList.get(14));
		clientCustomizeDo.setDischargeEndMinute1(valList.get(15));

		clientCustomizeDo.setChargeStartHour2(valList.get(17));
		clientCustomizeDo.setChargeStartMinute2(valList.get(18));
		clientCustomizeDo.setChargeEndHour2(valList.get(19));
		clientCustomizeDo.setChargeEndMinute2(valList.get(20));

		clientCustomizeDo.setDischargeStartHour2(valList.get(22));
		clientCustomizeDo.setDischargeStartMinute2(valList.get(23));
		clientCustomizeDo.setDischargeEndHour2(valList.get(24));
		clientCustomizeDo.setDischargeEndMinute2(valList.get(25));

		List<Integer> feedInList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 41037, 1, cloud);
		List<Integer> epsBatteryMinList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 41042, 1, cloud);
		List<Integer> dischargeToGridFlagList = ecosIotApi.readDevice(hybridSinglePhaseDO.getWifiSn(), 1, 40046, 1, cloud);

		if (feedInList.size() > 0) {
			customizeInfoDto.setMaxFeedIn(feedInList.get(0));
			clientCustomizeDo.setMaxFeedIn(feedInList.get(0));
		}

		if (epsBatteryMinList.size() > 0) {
			customizeInfoDto.setEpsBatteryMin(epsBatteryMinList.get(0));
			clientCustomizeDo.setEpsBatteryMin(epsBatteryMinList.get(0));
		}

		if (dischargeToGridFlagList.size() > 0) {
			customizeInfoDto.setDischargeToGridFlag(dischargeToGridFlagList.get(0));
			clientCustomizeDo.setDischargeToGridFlag(dischargeToGridFlagList.get(0));
		}

		ClientCustomizeDo clientCustomizeDo2 = clientCustomizeDo.convertClientCustomizeDo();

		ActionFlagUtil.assertTrue(clientCustomizeService.save(clientCustomizeDo2));
	}

	private void setCustomizeInfoDto(CustomizeInfoDto customizeInfoDto, List<Integer> valList) {
		val resLen = 27;
		if (valList.size() != resLen) {
			log.warn("参数长度错误");
			throw new EcosException(EcosExceptionEnum.INVALID_RESULT_LEN);
		}
		customizeInfoDto.setMinCapacity(valList.get(1));
		customizeInfoDto.setChargeUseMode(valList.get(2));

		customizeInfoDto.setCharge1StartTimeHour(valList.get(7));
		customizeInfoDto.setCharge1StartTimeMinute(valList.get(8));
		customizeInfoDto.setCharge1EndTimeHour(valList.get(9));
		customizeInfoDto.setCharge1EndTimeMinute(valList.get(10));

		customizeInfoDto.setDischarge1StartTimeHour(valList.get(12));
		customizeInfoDto.setDischarge1StartTimeMinute(valList.get(13));
		customizeInfoDto.setDischarge1EndTimeHour(valList.get(14));
		customizeInfoDto.setDischarge1EndTimeMinute(valList.get(15));

		customizeInfoDto.setCharge2StartTimeHour(valList.get(17));
		customizeInfoDto.setCharge2StartTimeMinute(valList.get(18));
		customizeInfoDto.setCharge2EndTimeHour(valList.get(19));
		customizeInfoDto.setCharge2EndTimeMinute(valList.get(20));

		customizeInfoDto.setDischarge2StartTimeHour(valList.get(22));
		customizeInfoDto.setDischarge2StartTimeMinute(valList.get(23));
		customizeInfoDto.setDischarge2EndTimeHour(valList.get(24));
		customizeInfoDto.setDischarge2EndTimeMinute(valList.get(25));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void writeCustomize(CustomizeInfoVo customizeInfoVo) {
		ClientCustomizeDo existsItem = clientCustomizeService.getOne(Wrappers.<ClientCustomizeDo>lambdaQuery()
				.eq(ClientCustomizeDo::getDeviceId, customizeInfoVo.getDeviceId()));
		ClientCustomizeDo customizeItem = Optional.ofNullable(existsItem)
				.orElseThrow(() -> new EcosException(EcosExceptionEnum.DEVICE_CONNECT_ERROR));

		// 当自定化策略选择收益优先模式时，用户需额外配置峰谷价差金额。
		if (customizeInfoVo.getStrategyMode() == 1) {
			Optional.ofNullable(customizeInfoVo.getPriceGap())
					.orElseThrow(() -> new EcosException(EcosExceptionEnum.INVALID_PARAM));
		}

		// 校验设备是否在线
		String timezone;
		HybridSinglePhaseDO deviceInfo;
		if (StrUtil.isNotBlank(customizeInfoVo.getTimezone())) {
			timezone = customizeInfoVo.getTimezone();
			deviceInfo = hubService.getById(Long.parseLong(customizeInfoVo.getDeviceId()));
		} else {
			Pair<String, HybridSinglePhaseDO> pair = authenticateDevice(customizeInfoVo.getDeviceId());
			timezone = pair.getKey();
			deviceInfo = pair.getValue();
			if (customizeInfoVo.getAutoHomeStrategy() != null && customizeInfoVo.getAutoHomeStrategy() == 1) {
				customizeItem.setTimezone(timezone);
			}
		}
		if (deviceInfo.getState() < 0) {
			throw new EcosException(EcosExceptionEnum.DEVICE_CONNECT_ERROR);
		}
		TimezoneUtil.convertUserTimezoneToGMT(customizeInfoVo, timezone);
		TimezoneUtil.convertUserTimezoneToGmt(customizeInfoVo.getChargingList(), timezone);
		TimezoneUtil.convertUserTimezoneToGmt(customizeInfoVo.getDischargingList(), timezone);



		List<Integer> params = customizeInfoVo.buildPostParams(customizeItem);
		List<Integer> extraParams = customizeInfoVo.buildExtraParams(customizeItem);
		List<Integer> abandonPvParams = customizeInfoVo.buildAbandonPvParams(customizeItem);

		String cloud = String.valueOf(deviceInfo.getDataSource());
		ecosIotApi.writeDevice(deviceInfo.getWifiSn(), 1, 41001, 27, params, cloud);
		DataResponse<OssGlobalConfigBo> globalConfig = ossGlobalConfigApi.getGlobalConfig();
		if (CollUtil.isNotEmpty(extraParams) && hasConfigFlag(deviceInfo.getEmsSoftwareVersion(), globalConfig, MULTI_CHARGE_FLAG)) {
			ecosIotApi.writeDevice(deviceInfo.getWifiSn(), 1, 41043, 100, extraParams, cloud);
		}
		if (CollUtil.isNotEmpty(abandonPvParams) && hasConfigFlag(deviceInfo.getEmsSoftwareVersion(), globalConfig, ABANDON_PV_FLAG)) {
			ecosIotApi.writeDevice(deviceInfo.getWifiSn(), 1, 41143, 24, abandonPvParams, cloud);
		}
		Optional.ofNullable(customizeInfoVo.getMaxFeedIn()).ifPresent(feedIn -> {
			Integer param = NumberUtil.validIntegerValue(feedIn, 0, 100, 0, false);
			ecosIotApi.writeDevice(deviceInfo.getWifiSn(), 1, 41037, 1, ListUtil.toLinkedList(param), cloud);
			customizeItem.setMaxFeedIn(param);
		});
		Optional.ofNullable(customizeInfoVo.getEpsBatteryMin()).ifPresent(epsBatteryMin -> {
			Integer param = NumberUtil.validIntegerValue(epsBatteryMin, 10, 100, 10, false);
			ecosIotApi.writeDevice(deviceInfo.getWifiSn(), 1, 41042, 1, ListUtil.toLinkedList(param), cloud);
			customizeItem.setEpsBatteryMin(param);
		});
		Optional.ofNullable(customizeInfoVo.getDischargeToGridFlag()).ifPresent(dischargeFlag -> {
			Integer param = NumberUtil.validIntegerValue(dischargeFlag, 0, 1, 0, false);
			ecosIotApi.writeDevice(deviceInfo.getWifiSn(), 1, 40046, 1, ListUtil.toLinkedList(param), cloud);
			customizeItem.setDischargeToGridFlag(param);
		});
		// 转换一下再去更新
		ClientCustomizeDo clientCustomizeDo2 = customizeItem.convertClientCustomizeDo();
		clientCustomizeDo2.buildExtraParams(customizeInfoVo);
		ActionFlagUtil.assertTrue(clientCustomizeService.updateById(clientCustomizeDo2));
	}

	/** 判断是否有新功能权限 */
	private boolean hasConfigFlag(String emsSoftwareVersion, DataResponse<OssGlobalConfigBo> globalConfig, String flag) {
		if (globalConfig != null) {
			JSONObject firmware = globalConfig.getData().getFirmware();
			if (firmware.containsKey("EMS_version")) {
				if (firmware.getJSONObject("EMS_version").containsKey(emsSoftwareVersion)) {
					List<String> optionList = firmware.getJSONObject("EMS_version").getJSONArray(emsSoftwareVersion).toList(String.class);
					return optionList.contains(flag);
				}
			}
		}
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void writeCustomizeV2(CustomizeInfoEzV2Vo customizeInfoEzV2Vo) {
		// 转换参数 customizeInfoEzV2Vo --> customizeInfoEzVo
		CustomizeInfoEzVo customizeInfoEzVo = convertCustomizeInfoEzVo(customizeInfoEzV2Vo);
		writeCustomize(TransformUtil.ezToCustomizeInfoVo(customizeInfoEzVo));
	}

	@Override
	public void backupLastTimeList(CustomizeInfoEzV2Vo param) {
		if (param.getChargeUseMode() != 1) {
			return;
		}
		clientCustomizeLastService.backupLastTimeList(param.getDeviceId(),
				param.getChargingList(), param.getDischargingList());
	}

	@Override
	public TimeListLastDTO queryLastTimeList(String deviceId) {
		return clientCustomizeLastService.queryLastTimeList(deviceId);
	}

	@Override
	public List<EleTimeZoneDto> getEleTimeZone() {
		InResponse<Object> eleTimeZone = ecosEleApi.getEleTimeZone();
		Object data = eleTimeZone.getData();
		return JSONUtil.toList(JSONUtil.parseArray(data), EleTimeZoneDto.class);
	}

	@Override
	public List<EleRegionDTO> getEleCountryRegion() {
		InResponse<Object> eleCountryRegion = ecosEleApi.getEleCountryRegion("Entsoe");
		Object data = eleCountryRegion.getData();
		List<EleCountryRegionDto> itemList = JSONUtil.toList(JSONUtil.parseArray(data), EleCountryRegionDto.class);
		if (CollUtil.isEmpty(itemList)) {
			return Collections.emptyList();
		}
		List<EleRegionDTO> resList = new ArrayList<>();
		Map<String, List<EleCountryRegionDto>> group = itemList.stream()
				.collect(Collectors.groupingBy(i -> String.format("%s,%s", i.getCountryCn(), i.getCountryEn())));
		group.forEach((k,v) -> resList.add(EleRegionDTO.builder()
						.countryCn(k.split(Constants.COMMA)[0])
						.countryEn(k.split(Constants.COMMA)[1])
						.nationalFlag(v.get(0).getNationalFlag())
						.regions(v.stream().map(i -> EleRegionDTO.Region.builder()
										.regionEn(i.getRegionEn())
										.regionCn(i.getRegionCn())
										.build())
								.collect(Collectors.toList()))
				.build()));
		return resList;
	}

	@Override
	public List<EleDataSourceDto> getEleDataSource() {
		InResponse<Object> eleDataSource = ecosEleApi.getEleDataSource();
		Object data = eleDataSource.getData();
		return JSONUtil.toList(JSONUtil.parseArray(data), EleDataSourceDto.class);
	}

	@Override
	public List<EleDayAheadPriceDto> getEleAheadPrice(AheadPriceVo aheadPriceVo) {
		aheadPriceVo.setTimezone(SecurityUtil.getClientUserDo().getTimeZone());
		return clientElePriceWholesaleService.getEleAheadPrice(aheadPriceVo);
	}

	@Override
	public List<EleStrategyDTO> strategyPreview(EleStrategyPreviewVO param) {
		// 根据电价导入类型，自定义电价保持现状，家庭电价走家庭电价逻辑
		List<EleDayAheadPriceDto> elePriceList;
		if (PriceImportTypeEnum.HOME.getCode().equals(param.getPriceImportType())) {
			ClientHomeDo homeInfo = clientHomeService.getById(param.getHomeId());
			// 走家庭电价
			HomeElePriceService homeElePriceService = strategyService.chooseHomeElePriceService(homeInfo.getElePriceType());
			elePriceList = homeElePriceService.queryOriginalElePrice(String.valueOf(homeInfo.getId()), param.getTime(), param.getTimezone());
		} else {
			// 走自定义电价类型
			elePriceList = clientElePriceWholesaleService.getEleAheadPrice(AheadPriceVo.builder()
					.region(Collections.singletonList(param.getRegion()))
					.intervalSeconds(3600)
					.time(param.getTime())
					.timezone(param.getTimezone())
					.priceUnit(param.getPriceUnit())
					.build());
		}
		List<EleStrategyDTO> eleStrategyRes;
		if (StrategyModeEnum.DEF.getCode().equals(param.getStrategyMode())) {
			// 根据电价生成电价策略
			eleStrategyRes = clientEleStrategyService.calStrategyWithNormal(elePriceList, param);
		} else if (param.getDeviceId() != null || StrategyModeEnum.HOME_LOAD.getCode().equals(param.getStrategyMode())) {
			eleStrategyRes = clientEleStrategyService.calStrategyWithNormal(elePriceList, param);
			// 如果开启了家庭负载优化策略，则加入家庭负载功率逻辑
			Map<Integer, Integer> eleHomePowerRes = clientEleStrategyService.homePowerGraph(
					EleHomeStrategyPreviewVO.builder()
							.deviceId(param.getDeviceId())
							.time(param.getTime())
							.timezone(param.getTimezone())
							.dayType(param.getDayType()).build());
			clientEleStrategyService.calStrategyWithHomePower(eleStrategyRes, eleHomePowerRes, param.getTimezone());
		} else if (StrategyModeEnum.EARNINGS.getCode().equals(param.getStrategyMode())) {
			eleStrategyRes = clientEleStrategyService.calStrategyWithEarning(elePriceList, param);
		} else if (StrategyModeEnum.NEGATIVE.getCode().equals(param.getStrategyMode())) {
			eleStrategyRes = clientEleStrategyService.calStrategyWithNegative(elePriceList, param);
		} else {
			return Collections.emptyList();
		}
		return eleStrategyRes;
	}

	@Override
	public CustomizeInfoEzDto queryStrategy(EleStrategyPreviewVO param) {
		List<EleStrategyDTO> eleStrategyList = strategyPreview(param);
		return clientEleStrategyService.queryStrategy(eleStrategyList, param.getTimezone());
	}

	@Override
    public RippleControlDTO rippleControlQuery(Long deviceId) {
		HybridSinglePhaseDO deviceInfo = hubService.getById(deviceId);
		Assert.notNull(deviceInfo, "device not found");
		if (deviceInfo.getState() <= 0) {
			throw new EcosException(EcosExceptionEnum.DEVICE_CONNECT_ERROR);
		}
		List<Integer> resList;
		try {
			resList = ecosIotApi.readDevice(deviceInfo.getWifiSn(), 1, 40051, 16,
					String.valueOf(deviceInfo.getDataSource()));
		} catch (Exception e) {
			log.error("rippleControlQuery error", e);
			return RippleControlDTO.builder().deviceId(deviceId).rippleMap(new LinkedHashMap<>()).build();
		}
		LinkedHashMap<Integer, Integer> resMap = new LinkedHashMap<>(16);
		for (int i = 0; i < resList.size(); i++) {
			short signedNum = (short) (resList.get(i) & 0xFFFF);
			resMap.put(i, (int) signedNum);
		}
		return RippleControlDTO.builder().deviceId(deviceId).rippleMap(resMap).build();
    }

	@Override
	public void rippleControlConfig(RippleControlDTO param) {
		HybridSinglePhaseDO deviceInfo = hubService.getById(param.getDeviceId());
		Assert.notNull(deviceInfo, "device not found");
		if (deviceInfo.getState() < 0) {
			throw new EcosException(EcosExceptionEnum.DEVICE_CONNECT_ERROR);
		}
		LinkedHashMap<Integer, Integer> rippleMap = param.getRippleMap();
		Assert.isTrue(rippleMap.size() == 16, "rippleMap size must be 16");
		ecosIotApi.writeDevice(deviceInfo.getWifiSn(), 1, 40051, 16, new ArrayList<>(rippleMap.values()),
				String.valueOf(deviceInfo.getDataSource()));
	}

    @Override
    public DynamicExportDTO dynamicExport(String deviceName) {
		HybridSinglePhaseDO deviceInfo = hubService.getByDeviceName(deviceName);
		Assert.notNull(deviceInfo, "device not found");
		if (deviceInfo.getState() < 0) {
			throw new EcosException(EcosExceptionEnum.DEVICE_CONNECT_ERROR);
		}
		return hubService.dynamicExport(deviceName);
    }

	@Override
	public DynamicDesignDTO designInfo(String deviceName) {
		HybridSinglePhaseDO deviceInfo = hubService.getByDeviceName(deviceName);
		Assert.notNull(deviceInfo, "device not found");
		if (deviceInfo.getState() < 0) {
			throw new EcosException(EcosExceptionEnum.DEVICE_CONNECT_ERROR);
		}
		return hubService.designInfo(deviceName);
	}

	@Override
	public void dynamicSave(DynamicSaveVO param) {
		hubService.dynamicSave(param);
	}

	@Override
	public DynamicTestDTO dynamicTest(String deviceName) {
		return hubService.dynamicTest(deviceName);
	}

	@Override
	public void dynamicSwitch(DynamicSwitchVO param) {
		HybridSinglePhaseDO deviceInfo = hubService.getByDeviceName(param.getDeviceName());
		Assert.notNull(deviceInfo, "device not found");
		if (deviceInfo.getState() < 0) {
			throw new EcosException(EcosExceptionEnum.DEVICE_CONNECT_ERROR);
		}
		hubService.dynamicSwitch(param);
	}

	private Pair<String, HybridSinglePhaseDO> authenticateDevice(String deviceId) {
		ClientUserDo clientUserDo = SecurityUtil.getClientUserDo();

		// 如果家庭中有这台设备就不做校验
		Boolean checkUserHasDevice = v2HomeAdapter.checkUserHasDevice(clientUserDo, deviceId);
		if (checkUserHasDevice) {
			HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.parseLong(deviceId));
			return Pair.of(clientUserDo.getTimeZone(), hybridSinglePhaseDO);
		}

		List<MiddleClientUserDeviceDo> bindList = middleClientUserDeviceService.list(Wrappers
				.<MiddleClientUserDeviceDo>lambdaQuery()
				.eq(MiddleClientUserDeviceDo::getUserId, clientUserDo.getId())
				.eq(MiddleClientUserDeviceDo::getDeviceId, deviceId));

		if (bindList.size() == 0) {
			log.warn("无权限对设备进行操作");
			throw new UnauthorizedException(EcosExceptionEnum.INVALID_ACTION_TO_DEVICE);
		}
		HybridSinglePhaseDO hybridSinglePhaseDO = hubService.getById(Long.parseLong(deviceId));
		return Pair.of(clientUserDo.getTimeZone(), hybridSinglePhaseDO);
	}

	private CustomizeInfoEzVo convertCustomizeInfoEzVo(CustomizeInfoEzV2Vo customizeInfoEzV2Vo) {

		CustomizeInfoEzVo customizeInfoEzVo = new CustomizeInfoEzVo();
		CglibUtil.copy(customizeInfoEzV2Vo,customizeInfoEzVo);
		// 解决浅拷贝问题
		customizeInfoEzVo.setChargingList(customizeInfoEzV2Vo.copyChargingList());
		customizeInfoEzVo.setDischargingList(customizeInfoEzV2Vo.copyDischargingList());
		Integer mode = customizeInfoEzV2Vo.getChargeUseMode();
		val selfPowered = 0;
		val loadShifting = 1;
		val backup = 2;

		switch (mode) {
			case selfPowered:
				if (customizeInfoEzVo.getMinCapacity() == null) {
					customizeInfoEzVo.setMinCapacity(customizeInfoEzV2Vo.getSelfSoc());
				}
//				customizeInfoEzVo.setEpsBatteryMin(customizeInfoEzV2Vo.getSelfEpsBat());
				if (customizeInfoEzVo.getMaxFeedIn() == null) {
					customizeInfoEzVo.setMaxFeedIn(customizeInfoEzV2Vo.getSelfFeedIn());
				}
				break;
			case loadShifting:
				if (customizeInfoEzVo.getMinCapacity() == null) {
					customizeInfoEzVo.setMinCapacity(customizeInfoEzV2Vo.getRegularSoc());
				}
//				customizeInfoEzVo.setEpsBatteryMin(customizeInfoEzV2Vo.getRegularEpsBat());
				if (customizeInfoEzVo.getMaxFeedIn() == null) {
					customizeInfoEzVo.setMaxFeedIn(customizeInfoEzV2Vo.getRegularFeedIn());
				}
				break;
			case backup:
				if (customizeInfoEzVo.getMinCapacity() == null) {
					customizeInfoEzVo.setMinCapacity(customizeInfoEzV2Vo.getBackupSoc());
				}
//				customizeInfoEzVo.setEpsBatteryMin(customizeInfoEzV2Vo.getBackupEpsBat());
				if (customizeInfoEzVo.getMaxFeedIn() == null) {
					customizeInfoEzVo.setMaxFeedIn(customizeInfoEzV2Vo.getBackupFeedIn());
				}
				break;
			default:
				log.warn("参数错误");
				throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}

		return customizeInfoEzVo;
	}
}
