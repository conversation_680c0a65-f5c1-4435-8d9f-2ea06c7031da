package com.weihengtech.ecos.utils;

import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.enums.ele.ElePriceTypeDetailEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 电价类型检测测试类
 */
public class ElectricityPriceTypeTest {

    public static void main(String[] args) {
        System.out.println("========== 电价类型检测测试 ==========\n");

        // 测试1：固定电价
        testFixedPrice();

        // 测试2：分时电价
        testTimeOfUsePrice();

        // 测试3：批发电价-1小时
        testWholesale1HPrice();

        // 测试4：批发电价-30分钟
        testWholesale30MinPrice();

        // 测试5：批发电价-15分钟
        testWholesale15MinPrice();

        // 测试6：零售商固定电价
        testRetailerFixedPrice();

        // 测试7：零售商分时电价
        testRetailerTimeOfUsePrice();

        // 测试8：零售商动态电价-1小时
        testRetailerDynamic1HPrice();

        // 测试9：特殊案例 - Intelligent Octopus（48条记录但价格变化少）
        testIntelligentOctopusPrice();
    }

    /**
     * 测试1：固定电价 - 1条记录覆盖全天
     */
    private static void testFixedPrice() {
        System.out.println("【测试1】固定电价 - 1条记录覆盖全天");
        List<EleDayAheadPriceDto> priceList = new ArrayList<>();

        long baseTime = LocalDateTime.of(2025, 8, 20, 0, 0).toEpochSecond(ZoneOffset.UTC);

        priceList.add(EleDayAheadPriceDto.builder()
                .country("UK")
                .region("London")
                .currency("GBP")
                .type("Day-Ahead")
                .average(new BigDecimal("0.25"))
                .startTimeUnix(baseTime)
                .build());

        runTest(priceList, "固定电价");
    }

    /**
     * 测试2：分时电价 - 4个时段，不同价格
     */
    private static void testTimeOfUsePrice() {
        System.out.println("【测试2】分时电价 - 4个时段");
        List<EleDayAheadPriceDto> priceList = new ArrayList<>();

        long baseTime = LocalDateTime.of(2025, 8, 20, 0, 0).toEpochSecond(ZoneOffset.UTC);

        // 夜间低谷 00:00-06:00
        priceList.add(createPriceDto(baseTime, new BigDecimal("0.15")));
        // 早高峰 06:00-09:00
        priceList.add(createPriceDto(baseTime + 6 * 3600, new BigDecimal("0.35")));
        // 日间平段 09:00-17:00
        priceList.add(createPriceDto(baseTime + 9 * 3600, new BigDecimal("0.25")));
        // 晚高峰 17:00-22:00
        priceList.add(createPriceDto(baseTime + 17 * 3600, new BigDecimal("0.40")));
        // 夜间 22:00-24:00
        priceList.add(createPriceDto(baseTime + 22 * 3600, new BigDecimal("0.15")));

        runTest(priceList, "分时电价");
    }

    /**
     * 测试3：批发电价 - 1小时间隔（24条记录）
     */
    private static void testWholesale1HPrice() {
        System.out.println("【测试3】批发电价 - 1小时间隔（24条记录）");
        List<EleDayAheadPriceDto> priceList = new ArrayList<>();

        long baseTime = LocalDateTime.of(2025, 8, 20, 0, 0).toEpochSecond(ZoneOffset.UTC);
        Random random = new Random(42);

        for (int hour = 0; hour < 24; hour++) {
            // 模拟价格波动：基础价格 + 随机波动
            double basePrice = 0.20 + 0.15 * Math.sin(hour * Math.PI / 12); // 正弦波模拟日内波动
            double randomFactor = 0.05 * (random.nextDouble() - 0.5); // 随机波动
            BigDecimal price = new BigDecimal(String.format("%.3f", basePrice + randomFactor));

            priceList.add(createPriceDto(baseTime + hour * 3600, price));
        }

        runTest(priceList, "批发电价-1h");
    }

    /**
     * 测试4：批发电价 - 30分钟间隔（48条记录）
     */
    private static void testWholesale30MinPrice() {
        System.out.println("【测试4】批发电价 - 30分钟间隔（48条记录）");
        List<EleDayAheadPriceDto> priceList = new ArrayList<>();

        long baseTime = LocalDateTime.of(2025, 8, 20, 0, 0).toEpochSecond(ZoneOffset.UTC);
        Random random = new Random(42);

        for (int i = 0; i < 48; i++) {
            double hour = i / 2.0;
            double basePrice = 0.20 + 0.15 * Math.sin(hour * Math.PI / 12);
            double randomFactor = 0.08 * (random.nextDouble() - 0.5);
            BigDecimal price = new BigDecimal(String.format("%.3f", basePrice + randomFactor));

            priceList.add(createPriceDto(baseTime + i * 1800, price)); // 1800秒 = 30分钟
        }

        runTest(priceList, "批发电价-30min");
    }

    /**
     * 测试5：批发电价 - 15分钟间隔（96条记录）
     */
    private static void testWholesale15MinPrice() {
        System.out.println("【测试5】批发电价 - 15分钟间隔（96条记录）");
        List<EleDayAheadPriceDto> priceList = new ArrayList<>();

        long baseTime = LocalDateTime.of(2025, 8, 20, 0, 0).toEpochSecond(ZoneOffset.UTC);
        Random random = new Random(42);

        for (int i = 0; i < 96; i++) {
            double hour = i / 4.0;
            double basePrice = 0.20 + 0.15 * Math.sin(hour * Math.PI / 12);
            double randomFactor = 0.10 * (random.nextDouble() - 0.5);
            BigDecimal price = new BigDecimal(String.format("%.3f", basePrice + randomFactor));

            priceList.add(createPriceDto(baseTime + i * 900, price)); // 900秒 = 15分钟
        }

        runTest(priceList, "批发电价-15min");
    }

    /**
     * 测试6：零售商固定电价（带税费）
     */
    private static void testRetailerFixedPrice() {
        System.out.println("【测试6】零售商固定电价（带税费）");
        List<EleDayAheadPriceDto> priceList = new ArrayList<>();

        long baseTime = LocalDateTime.of(2025, 8, 20, 0, 0).toEpochSecond(ZoneOffset.UTC);

        EleDayAheadPriceDto dto = EleDayAheadPriceDto.builder()
                .country("UK")
                .region("London")
                .currency("GBP")
                .type("Day-Ahead")
                .average(new BigDecimal("0.30"))
                .startTimeUnix(baseTime)
                .tax(new BigDecimal("0.05")) // 税费
                .build();

        priceList.add(dto);

        runTest(priceList, "零售商固定电价");
    }

    /**
     * 测试7：零售商分时电价（带税费）
     */
    private static void testRetailerTimeOfUsePrice() {
        System.out.println("【测试7】零售商分时电价（带税费）");
        List<EleDayAheadPriceDto> priceList = new ArrayList<>();

        long baseTime = LocalDateTime.of(2025, 8, 20, 0, 0).toEpochSecond(ZoneOffset.UTC);
        BigDecimal tax = new BigDecimal("0.05");

        // 创建分时电价数据
        priceList.add(createPriceDtoWithTax(baseTime, new BigDecimal("0.18"), tax));
        priceList.add(createPriceDtoWithTax(baseTime + 7 * 3600, new BigDecimal("0.35"), tax));
        priceList.add(createPriceDtoWithTax(baseTime + 19 * 3600, new BigDecimal("0.18"), tax));

        runTest(priceList, "零售商分时电价");
    }

    /**
     * 测试8：零售商动态电价 - 1小时（带税费）
     */
    private static void testRetailerDynamic1HPrice() {
        System.out.println("【测试8】零售商动态电价 - 1小时（带税费）");
        List<EleDayAheadPriceDto> priceList = new ArrayList<>();

        long baseTime = LocalDateTime.of(2025, 8, 20, 0, 0).toEpochSecond(ZoneOffset.UTC);
        BigDecimal tax = new BigDecimal("0.05");
        Random random = new Random(42);

        for (int hour = 0; hour < 24; hour++) {
            double basePrice = 0.25 + 0.20 * Math.sin(hour * Math.PI / 12);
            double randomFactor = 0.05 * (random.nextDouble() - 0.5);
            BigDecimal price = new BigDecimal(String.format("%.3f", basePrice + randomFactor));

            priceList.add(createPriceDtoWithTax(baseTime + hour * 3600, price, tax));
        }

        runTest(priceList, "零售商动态电价-1h");
    }

    /**
     * 测试9：特殊案例 - Intelligent Octopus
     * 48条记录但只有少数价格变化
     */
    private static void testIntelligentOctopusPrice() {
        System.out.println("【测试9】特殊案例 - Intelligent Octopus（48条记录，少量价格变化）");
        List<EleDayAheadPriceDto> priceList = new ArrayList<>();

        long baseTime = LocalDateTime.of(2025, 8, 20, 0, 0).toEpochSecond(ZoneOffset.UTC);

        BigDecimal lowPrice = new BigDecimal("0.075");  // 低谷价
        BigDecimal normalPrice = new BigDecimal("0.25"); // 正常价

        for (int i = 0; i < 48; i++) {
            BigDecimal price;
            int hour = i / 2;

            // 模拟Intelligent Octopus: 大部分时间正常价，少数时段低价
            if (hour >= 2 && hour <= 5) {  // 凌晨2-5点低价
                price = lowPrice;
            } else if (hour >= 13 && hour <= 14) {  // 下午1-2点低价
                price = lowPrice;
            } else {
                price = normalPrice;
            }

            priceList.add(createPriceDto(baseTime + i * 1800, price));
        }

        runTest(priceList, "分时电价（Intelligent Octopus）");
    }

    /**
     * 创建电价DTO
     */
    private static EleDayAheadPriceDto createPriceDto(long startTime, BigDecimal price) {
        return EleDayAheadPriceDto.builder()
                .country("UK")
                .region("London")
                .currency("GBP")
                .type("Day-Ahead")
                .average(price)
                .startTimeUnix(startTime)
                .build();
    }

    /**
     * 创建带税费的电价DTO
     */
    private static EleDayAheadPriceDto createPriceDtoWithTax(long startTime, BigDecimal price, BigDecimal tax) {
        return EleDayAheadPriceDto.builder()
                .country("UK")
                .region("London")
                .currency("GBP")
                .type("Day-Ahead")
                .average(price)
                .startTimeUnix(startTime)
                .tax(tax)
                .build();
    }

    /**
     * 运行测试并输出结果
     */
    private static void runTest(List<EleDayAheadPriceDto> priceList, String expectedType) {
        try {
            // 获取检测结果
            ElePriceTypeDetailEnum detectedType = ElectricityPriceTypeUtil.detectPriceType(priceList);


            // 输出结果
            System.out.println("预期类型: " + expectedType);
            System.out.println("检测结果: " + detectedType.getName());

            // 判断是否正确
            boolean isCorrect = expectedType.contains(detectedType.getName()) ||
                    detectedType.getName().contains(expectedType);
            System.out.println("测试结果: " + (isCorrect ? "✓ 通过" : "✗ 失败"));

        } catch (Exception e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("----------------------------------------\n");
    }
}