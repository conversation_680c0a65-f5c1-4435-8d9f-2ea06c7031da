package com.weihengtech.ecos.utils;

import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.enums.ele.ElePriceTypeDetailEnum;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: jiahao.jin
 * @create: 2025-08-19 16:34
 * @description: 电价类型判断工具类
 */
public class ElectricityPriceTypeUtil {
    // 一小时的秒数
    private static final long SECONDS_PER_HOUR = 60 * 60;
    // 30分钟的秒数
    private static final long SECONDS_PER_30MIN = 30 * 60;
    // 15分钟的秒数
    private static final long SECONDS_PER_15MIN = 15 * 60;

    /**
     * 判断电价类型
     *
     * @param priceList 电价列表
     * @return 电价类型枚举
     */
    public static ElePriceTypeDetailEnum detectPriceType(List<EleDayAheadPriceDto> priceList) {
        if (priceList == null || priceList.isEmpty()) {
            throw new IllegalArgumentException("电价列表不能为空");
        }

        // 按时间戳排序
        List<EleDayAheadPriceDto> sortedList = priceList.stream()
                .sorted(Comparator.comparing(EleDayAheadPriceDto::getStartTimeUnix))
                .collect(Collectors.toList());

        // 1. 判断固定电价
        if (isFixedPrice(sortedList)) {
            return ElePriceTypeDetailEnum.FIXED;
        }

        // 3. 根据记录数和时间间隔判断
        long timeInterval = detectTimeInterval(sortedList);
        int recordCount = sortedList.size();

        // 15分钟间隔（96条记录）
        if (timeInterval == SECONDS_PER_15MIN || recordCount == 96) {
            return ElePriceTypeDetailEnum.INTERVAL_15MIN;
        }

        // 30分钟间隔（48条记录）
        if (timeInterval == SECONDS_PER_30MIN || recordCount == 48) {
            return ElePriceTypeDetailEnum.INTERVAL_30MIN;
        }

        // 1小时间隔（24条记录）
        if (timeInterval == SECONDS_PER_HOUR || recordCount == 24) {
            return ElePriceTypeDetailEnum.INTERVAL_1H;
        }

        // 默认根据记录数判断
        return detectByRecordCount(recordCount);
    }

    /**
     * 检测时间间隔（取最常见的间隔）
     */
    private static long detectTimeInterval(List<EleDayAheadPriceDto> sortedList) {
        if (sortedList.size() < 2) {
            return 0;
        }

        Map<Long, Integer> intervalCount = new HashMap<>();
        for (int i = 1; i < sortedList.size(); i++) {
            long interval = sortedList.get(i).getStartTimeUnix() - sortedList.get(i - 1).getStartTimeUnix();
            intervalCount.put(interval, intervalCount.getOrDefault(interval, 0) + 1);
        }

        // 返回最常见的时间间隔
        return intervalCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(0L);
    }

    /**
     * 判断是否为固定电价
     */
    private static boolean isFixedPrice(List<EleDayAheadPriceDto> sortedList) {
        if (sortedList.size() == 1) {
            return true;
        }

        // 如果所有价格都相同，也认为是固定电价
        BigDecimal firstPrice = sortedList.get(0).getAverage();
        return sortedList.stream()
                .allMatch(dto -> dto.getAverage().compareTo(firstPrice) == 0);
    }

    /**
     * 根据记录数判断电价类型
     */
    private static ElePriceTypeDetailEnum detectByRecordCount(int recordCount) {
        if (recordCount == 96) {
            return ElePriceTypeDetailEnum.INTERVAL_15MIN;
        } else if (recordCount == 48) {
            return ElePriceTypeDetailEnum.INTERVAL_30MIN;
        } else if (recordCount == 24) {
            return ElePriceTypeDetailEnum.INTERVAL_1H;
        } else {
            // 默认返回分时电价
            return ElePriceTypeDetailEnum.TIME_OF_USE;
        }
    }
}
