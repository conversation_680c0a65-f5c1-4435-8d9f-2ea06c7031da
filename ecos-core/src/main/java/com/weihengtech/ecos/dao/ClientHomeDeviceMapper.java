package com.weihengtech.ecos.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.weihengtech.ecos.model.dos.ClientHomeDeviceDo;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 家庭-设备关联表映射表接口
 * @author: jiahao.jin
 * @create: 2024-01-21 11:33
 **/
@DS("ecos")
public interface ClientHomeDeviceMapper extends BaseMapper<ClientHomeDeviceDo> {

    List<ClientHomeDeviceDo> getAllMasterHomeDevice(@Param("timezoneList") List<String> timezoneList);

    @Select("SELECT h.*\n" +
            "from client_home_device d\n" +
            "join client_home h\n" +
            "on h.id = d.home_id\n" +
            "where d.device_id = #{deviceId}")
    ClientHomeDo getHomeInfoByDeviceId(@Param("deviceId") Long deviceId);
}
