package com.weihengtech.ecos.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dtos.customize.StrategyCustomizeDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@DS("ecos")
public interface ClientCustomizeMapper extends BaseMapper<ClientCustomizeDo> {

    List<StrategyCustomizeDTO> getAutoStrategyDevices(@Param("timezoneList") List<String> timezoneList);

    List<ClientCustomizeDo> getAutoStrategyDevicesByIds(@Param("deviceIds") List<Long> deviceIds);

    List<StrategyCustomizeDTO> getFeedInProhibitionDevices(@Param("timezoneList") List<String> timezoneList);
}
