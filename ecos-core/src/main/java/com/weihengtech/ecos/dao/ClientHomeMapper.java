package com.weihengtech.ecos.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.weihengtech.ecos.model.dos.ClientHomeDo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @program: ecos-server
 * @description: 家庭表映射类
 * @author: jiahao.jin
 * @create: 2024-01-21 11:24
 **/
@DS("ecos")
public interface ClientHomeMapper extends BaseMapper<ClientHomeDo> {

    /**
     * 查询开启了家庭电价的家庭
     * @return
     */
    List<ClientHomeDo> queryHomePriceHomeList(@Param("timezoneList") List<String> timezoneList);
}
