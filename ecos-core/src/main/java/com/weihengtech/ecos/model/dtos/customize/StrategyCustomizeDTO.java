package com.weihengtech.ecos.model.dtos.customize;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/24 15:19
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "自定义信息回参")
public class StrategyCustomizeDTO {

	// 家庭ID
	private Long homeId;
	// 设备ID
	private Long deviceId;
	// 并网预留SOC
	private Integer batteryMin;
	// 馈网功率比例
	private Integer maxFeedIn;
	// 电价地区
	private String region;
	// 开启自动策略
	private Integer autoStrategy;
	// 开启联动家庭负载自动策略
	private Integer autoHomeStrategy;
	// 策略模式
	private Integer strategyMode;
	// 自定义充电功率
	private Integer defChargePower;
	// 自定义放电功率
	private Integer defDischargePower;
	// 设备关联时区
	private String timezone;
	// 电价导入类型：0：自定义|1：家庭电价
	private Integer priceImportType;
	// 购电税费
	private BigDecimal purchaseTax;
	// 峰谷价差
	private BigDecimal priceGap;
}
