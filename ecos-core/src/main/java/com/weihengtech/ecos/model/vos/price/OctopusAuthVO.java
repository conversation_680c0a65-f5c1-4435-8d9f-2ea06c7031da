package com.weihengtech.ecos.model.vos.price;

import com.weihengtech.ecos.consts.RequestConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:27
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OctopusAuthVO {

	@ApiModelProperty(name = "variables", value = "变量", required = true)
	private Variable variables;

	@ApiModelProperty(name = "query", value = "查询语句", required = true)
	private String query = RequestConstants.OCTOPUS_AUTH_PARAM;

	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	@Data
	public static class Variable {

		@ApiModelProperty(name = "accountNumber", value = "户号", required = true)
		private Input input;

		@Data
		@NoArgsConstructor
		@AllArgsConstructor
		@Builder
		public static class Input {

			@ApiModelProperty(name = "APIKey", value = "APIKey", required = true)
			private String APIKey;
		}
	}
}
