package com.weihengtech.ecos.model.dtos.app.home;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ecos-server
 * @description: 家庭详情信息类
 * @author: jiahao.jin
 * @create: 2024-01-23 17:26
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class V2HomeInfoDto {

    @ApiModelProperty(name = "homeId", value = "家庭id", required = true)
    private String homeId;

    @ApiModelProperty(name = "homeName", value = "家庭名称", required = true)
    private String homeName;

    @ApiModelProperty(name = "homeType", value = "家庭类型，0：被分享设备家庭，1：普通家庭", required = true)
    private Integer homeType;

    @ApiModelProperty(name = "longitude", value = "经度")
    private Double longitude;

    @ApiModelProperty(name = "latitude", value = "纬度")
    private Double latitude;

    @ApiModelProperty(name = "homeDeviceNumber", value = "家庭设备数量", required = true)
    private Integer homeDeviceNumber;

    @ApiModelProperty(name = "relationType", value = "在该家庭中所处角色(0-成员|1-所有者)", required = true)
    private Integer relationType;

    @ApiModelProperty(name = "createTime", value = "家庭创建时间", required = true)
    private Long createTime;

    @ApiModelProperty(name = "updateTime", value = "家庭更新时间", required = true)
    private Long updateTime;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "电价类型")
    private Integer elePriceType;

    @ApiModelProperty(value = "电价细分类型")
    private Integer elePriceDetailType;

    @ApiModelProperty(value = "时区")
    private String timeZone;

    @ApiModelProperty(value = "时区id")
    private String timeZoneId;

    @ApiModelProperty(value = "时区名称")
    private String timezoneName;
}
