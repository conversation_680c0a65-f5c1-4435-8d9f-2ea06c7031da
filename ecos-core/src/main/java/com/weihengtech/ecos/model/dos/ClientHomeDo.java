package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: ecos-server
 * @description: 家庭表
 * @author: jiahao.jin
 * @create: 2024-01-21 11:07
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_home")
public class ClientHomeDo {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    // 家庭名称
    private String homeName;

    // 0：被分享设备家庭，1：普通家庭
    private Integer homeType;

    // 经度
    private Double longitude;

    // 纬度
    private Double latitude;

    // 币种
    private Integer currency;

    // 电价类型
    private Integer elePriceType;

    // 电价细分类型
    private Integer elePriceDetailType;

    // 时区
    private String timezone;

    private Long createTime;

    private Long updateTime;
}
