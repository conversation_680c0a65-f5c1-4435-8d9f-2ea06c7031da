package com.weihengtech.ecos.model.dtos.ele;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/10/31 14:51
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "电价自动策略数据")
public class EleStrategyDTO {

    @ApiModelProperty(name = "startTimeUnix", value = "预测时间点，秒级时间戳")
    private Long startTimeUnix;

    @ApiModelProperty(name = "homePower", value = "家庭负载功率")
    private Integer homePower;

    @ApiModelProperty(name = "power", value = "功率")
    private Integer power;

    @ApiModelProperty(name = "chargeType", value = "充放电类型：充|否|放：-1|0|1")
    private Integer chargeType;

    @ApiModelProperty(name = "abandonPv", value = "是否弃光")
    private Integer abandonPv;

    private BigDecimal price;
}
