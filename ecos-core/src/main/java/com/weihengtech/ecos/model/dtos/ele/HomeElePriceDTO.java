package com.weihengtech.ecos.model.dtos.ele;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:27
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HomeElePriceDTO {

	@ApiModelProperty(name = "elePriceType", value = "电价类型")
	private Integer elePriceType;

	@ApiModelProperty(name = "elePriceDetailType", value = "电价细分类型")
	private Integer elePriceDetailType;

	@ApiModelProperty(name = "priceInfo", value = "电价信息")
	private Object priceInfo;

}
