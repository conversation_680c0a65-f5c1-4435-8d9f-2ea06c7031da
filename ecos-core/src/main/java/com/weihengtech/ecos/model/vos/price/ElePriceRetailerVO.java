package com.weihengtech.ecos.model.vos.price;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:27
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ElePriceRetailerVO {

	@ApiModelProperty(name = "homeId", value = "家庭Id", required = true)
	@NotNull(message = "err.not.blank")
	private String homeId;

	@ApiModelProperty(name = "retailer", value = "零售商平台", required = true)
	private Integer retailer;

	@ApiModelProperty(name = "token", value = "平台token", required = true)
	private String token;

	@ApiModelProperty(name = "retailerHomeId", value = "平台homeID", required = true)
	private String retailerHomeId;

	@ApiModelProperty(name = "currency", value = "币种")
	private Integer currency;

	@ApiModelProperty(name = "account", value = "账户")
	private String account;
}
