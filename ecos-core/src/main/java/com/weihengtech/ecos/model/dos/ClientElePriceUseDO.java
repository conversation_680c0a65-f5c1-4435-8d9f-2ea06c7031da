package com.weihengtech.ecos.model.dos;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_ele_price_use")
public class ClientElePriceUseDO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(name = "homeId", value = "家庭Id", required = true)
    private Long homeId;

    @ApiModelProperty(name = "startMonth", value = "开始月份", required = true)
    private Integer startMonth;

    @ApiModelProperty(name = "endMonth", value = "截止月份", required = true)
    private Integer endMonth;

    @ApiModelProperty(name = "type", value = "时段类型", required = true)
    private Integer type;

    @ApiModelProperty(name = "timeContent", value = "时段内容", required = true)
    private String timeContent;

    @TableField(exist = false)
    private List<Period> timeList;

    @ApiModelProperty(name = "purchasePrice", value = "购电电价", required = true)
    private BigDecimal purchasePrice;

    @ApiModelProperty(name = "purchaseTax", value = "购电税费", required = true)
    private BigDecimal purchaseTax;

    @ApiModelProperty(name = "feedInPrice", value = "馈网电价", required = true)
    private BigDecimal feedInPrice;

    @ApiModelProperty(name = "weekendSame", value = "周末是否相同", required = true)
    private Boolean weekendSame;

    @ApiModelProperty(name = "dayType", value = "工作日、周末", required = true)
    private Integer dayType;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MonthUsePrice {

        @ApiModelProperty(name = "startMonth", value = "开始月份", required = true)
        private Integer startMonth;

        @ApiModelProperty(name = "endMonth", value = "截止月份", required = true)
        private Integer endMonth;

        @ApiModelProperty(name = "weekendSame", value = "周末是否相同", required = true)
        private Boolean weekendSame;

        @ApiModelProperty(name = "monthUsePrices", value = "电价配置清单", required = true)
        private List<ClientElePriceUseDO.UsePriceDetail> monthUsePrices;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UsePriceDetail {

        @ApiModelProperty(name = "type", value = "电价类型", required = true)
        private Integer type;

        @ApiModelProperty(name = "timeList", value = "时间配置", required = true)
        private List<Period> timeList;

        @ApiModelProperty(name = "purchasePrice", value = "购电电价", required = true)
        private BigDecimal purchasePrice;

        @ApiModelProperty(name = "purchaseTax", value = "购电税费", required = true)
        private BigDecimal purchaseTax;

        @ApiModelProperty(name = "feedInPrice", value = "馈网电价", required = true)
        private BigDecimal feedInPrice;

        @ApiModelProperty(name = "dayType", value = "日期类型，0：周末，1：工作日", required = true)
        private Integer dayType;

        public ClientElePriceUseDO toUsePriceDetail(String homeId, Integer startMonth,
                                                    Integer endMonth, Boolean weekendSame) {
            return ClientElePriceUseDO.builder()
                    .homeId(Long.parseLong(homeId))
                    .startMonth(startMonth)
                    .endMonth(endMonth)
                    .type(type)
                    .timeContent(JSONUtil.toJsonStr(timeList))
                    .purchasePrice(purchasePrice)
                    .purchaseTax(purchaseTax)
                    .feedInPrice(feedInPrice)
                    .weekendSame(weekendSame)
                    .dayType(dayType)
                    .build();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Period {

        @ApiModelProperty(name = "start", value = "开始时间", required = true)
        private Integer start;

        @ApiModelProperty(name = "end", value = "结束时间", required = true)
        private Integer end;
    }

    public UsePriceDetail toUsePriceDetail() {
        return UsePriceDetail.builder()
                .type(type)
                .timeList(JSONUtil.toList(timeContent, Period.class))
                .purchasePrice(purchasePrice)
                .purchaseTax(purchaseTax)
                .feedInPrice(feedInPrice)
                .dayType(dayType)
                .build();
    }
}
