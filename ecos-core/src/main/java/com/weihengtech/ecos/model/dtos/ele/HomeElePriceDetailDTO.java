package com.weihengtech.ecos.model.dtos.ele;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: jiahao.jin
 * @create: 2025-08-25 16:35
 * @description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HomeElePriceDetailDTO {
    /**
     * 电价类型
     */
    @ApiModelProperty(name = "elePriceType", value = "电价类型")
    private Integer elePriceType;

    /**
     * 电价细分类型
     */
    @ApiModelProperty(name = "elePriceDetailType", value = "电价细分类型")
    private Integer elePriceDetailType;
}
