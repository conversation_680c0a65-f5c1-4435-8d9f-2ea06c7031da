package com.weihengtech.ecos.model.vos.customize;

import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "customize页面聚合入参")
public class CustomizeInfoEzVo {

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	@Min(value = 0)
	@Max(value = 2)
	@ApiModelProperty(name = "chargeUserMode", value = "selfPowered = 0;loadShifting = 1;backup = 2", required = true)
	private Integer chargeUseMode;

	@Min(value = 10)
	@Max(value = 100)
	private Integer minCapacity;

	@Min(value = 0)
	@Max(value = 100)
	private Integer maxFeedIn;

	@Min(value = 10)
	@Max(value = 100)
	private Integer epsBatteryMin;

	@Min(value = 0)
	@Max(value = 1)
	private Integer dischargeToGridFlag;

	@ApiModelProperty(name = "region", value = "电价地区")
	private String region;
	@ApiModelProperty(name = "autoStrategy", value = "开启自动策略")
	private Integer autoStrategy;
	@ApiModelProperty(name = "autoHomeStrategy", value = "开启家庭联动自动策略")
	private Integer autoHomeStrategy;
	@ApiModelProperty(name = "strategyMode", value = "策略模式")
	private Integer strategyMode;
	@ApiModelProperty(name = "defChargePower", value = "自定义充电功率")
	private Integer defChargePower;
	@ApiModelProperty(name = "defDischargePower", value = "自定义放电功率")
	private Integer defDischargePower;
	@ApiModelProperty(name = "timezone", value = "时区")
	private String timezone;
	@ApiModelProperty(name = "priceImportType", value = "电价导入类型：0：自定义|1：家庭电价")
	private Integer priceImportType = 0;
	@ApiModelProperty(name = "purchaseTax", value = "购电税费", required = true)
	private BigDecimal purchaseTax;
	@ApiModelProperty(name = "priceGap", value = "峰谷价差")
	private BigDecimal priceGap;

	// JBW need remove
	private List<ChargingStructDTO> chargingList;

	private List<ChargingStructDTO> dischargingList;
}
