package com.weihengtech.ecos.model.dtos.ele;

import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:27
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RetailerElePriceDTO {

	@ApiModelProperty(name = "elePriceType", value = "电价类型")
	private Integer elePriceDetailType;

	@ApiModelProperty(name = "priceInfo", value = "电价信息")
	private List<EleDayAheadPriceDto> priceList;

}
