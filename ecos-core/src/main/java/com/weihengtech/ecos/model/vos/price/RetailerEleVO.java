package com.weihengtech.ecos.model.vos.price;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:27
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RetailerEleVO {

	@ApiModelProperty(name = "retailer", value = "零售商类型", required = true)
	private Integer retailer;

	@ApiModelProperty(name = "token", value = "token|apikey", required = true)
	private String token;

	@ApiModelProperty(name = "tibberHomeId", value = "tibber homeId", required = true)
	private String retailerHomeId;

	@ApiModelProperty(name = "account", value = "零售商平台账号", required = true)
	private String account;

	@ApiModelProperty(name = "time", value = "查询时间，今天：0，明天：1", required = true)
	@NotNull(message = "err.not.null")
	private Integer time;

	private String timezone;
}
