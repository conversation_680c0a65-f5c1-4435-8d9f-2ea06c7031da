package com.weihengtech.ecos.model.dtos.customize;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "自定义信息回参")
public class CustomizeInfoDto {

	@ApiModelProperty(name = "chargeUseMode", value = "0: selfPowered; 1: loadShifting; 2:  backup", required = true)
	private Integer chargeUseMode;
	// 馈网功率比例
	private Integer maxFeedIn;
	// 预留SOC
	private Integer minCapacity;
	// 备电离网预留SOC
	private Integer epsBatteryMin;
	private Integer dischargeToGridFlag;

	private Integer charge1StartTimeHour;
	private Integer charge1StartTimeMinute;
	private Integer charge1EndTimeHour;
	private Integer charge1EndTimeMinute;
	// private Integer charge1Power = 6000;

	private Integer charge2StartTimeHour;
	private Integer charge2StartTimeMinute;
	private Integer charge2EndTimeHour;
	private Integer charge2EndTimeMinute;
	// private Integer charge2Power = 6000;

	private Integer discharge1StartTimeHour;
	private Integer discharge1StartTimeMinute;
	private Integer discharge1EndTimeHour;
	private Integer discharge1EndTimeMinute;
	// private Integer discharge1Power = 6000;

	private Integer discharge2StartTimeHour;
	private Integer discharge2StartTimeMinute;
	private Integer discharge2EndTimeHour;
	private Integer discharge2EndTimeMinute;
	// private Integer discharge2Power = 6000;

	// 自发自用SOC
	private Integer selfSoc;
	// 自发自用离网预留SOC
	private Integer selfEpsBat;
	// 自发自用馈网功率比例
	private Integer selfFeedIn;
	// 定时充放SOC
	private Integer regularSoc;
	// 定时充放离网预留SOC
	private Integer regularEpsBat;
	// 定时充放馈网功率比例
	private Integer regularFeedIn;
	// 备电预留SOC
	private Integer backupSoc;
	// 备电离网预留SOC
	private Integer backupEpsBat;
	// 备电预留馈网功率比例
	private Integer backupFeedIn;
	// ems软件版本
	private String emsSoftwareVersion;
	// dsp软件版本
	private String dsp1SoftwareVersion;
	// 额定功率
	private String ratedPower;
	// 电价地区
	private String region;
	// 开启自动策略
	private Integer autoStrategy;
	// 开启家庭联动自动策略
	private Integer autoHomeStrategy;
	// 策略模式
	private Integer strategyMode;
	// 自定义充电功率
	private Integer defChargePower;
	// 自定义放电功率
	private Integer defDischargePower;
	// 电价导入类型：0：自定义|1：家庭电价
	private Integer priceImportType;
	// 购电税费
	private BigDecimal purchaseTax;
	// 峰谷价差
	private BigDecimal priceGap;
	// 禁止馈网开关
	private Integer feedInProhibition;

	/** 12充列表 */
	private List<ChargingStructDTO> chargingList;
	/** 12放列表 */
	private List<ChargingStructDTO> dischargingList;

	public static void queryDataFromDb(ClientCustomizeDo clientCustomizeDo, CustomizeInfoDto customizeInfoDto) {
		customizeInfoDto.setMinCapacity(clientCustomizeDo.getBatteryMin());
		customizeInfoDto.setChargeUseMode(clientCustomizeDo.getChargeMode());
		customizeInfoDto.setCharge1StartTimeHour(clientCustomizeDo.getChargeStartHour1());
		customizeInfoDto.setCharge1StartTimeMinute(clientCustomizeDo.getChargeStartMinute1());
		customizeInfoDto.setCharge1EndTimeHour(clientCustomizeDo.getChargeEndHour1());
		customizeInfoDto.setCharge1EndTimeMinute(clientCustomizeDo.getChargeEndMinute1());
		customizeInfoDto.setCharge2StartTimeHour(clientCustomizeDo.getChargeStartHour2());
		customizeInfoDto.setCharge2StartTimeMinute(clientCustomizeDo.getChargeStartMinute2());
		customizeInfoDto.setCharge2EndTimeHour(clientCustomizeDo.getChargeEndHour2());
		customizeInfoDto.setCharge2EndTimeMinute(clientCustomizeDo.getChargeEndMinute2());
		customizeInfoDto.setDischarge1StartTimeHour(clientCustomizeDo.getDischargeStartHour1());
		customizeInfoDto.setDischarge1StartTimeMinute(clientCustomizeDo.getDischargeStartMinute1());
		customizeInfoDto.setDischarge1EndTimeHour(clientCustomizeDo.getDischargeEndHour1());
		customizeInfoDto.setDischarge1EndTimeMinute(clientCustomizeDo.getDischargeEndMinute1());
		customizeInfoDto.setDischarge2StartTimeHour(clientCustomizeDo.getDischargeStartHour2());
		customizeInfoDto.setDischarge2StartTimeMinute(clientCustomizeDo.getDischargeStartMinute2());
		customizeInfoDto.setDischarge2EndTimeHour(clientCustomizeDo.getDischargeEndHour2());
		customizeInfoDto.setDischarge2EndTimeMinute(clientCustomizeDo.getDischargeEndMinute2());
		customizeInfoDto.setMaxFeedIn(clientCustomizeDo.getMaxFeedIn());
		customizeInfoDto.setEpsBatteryMin(clientCustomizeDo.getEpsBatteryMin());
		customizeInfoDto.setDischargeToGridFlag(clientCustomizeDo.getDischargeToGridFlag());
		customizeInfoDto.setSelfSoc(clientCustomizeDo.getSelfSoc());
		customizeInfoDto.setSelfEpsBat(clientCustomizeDo.getSelfEpsBat());
		customizeInfoDto.setSelfFeedIn(clientCustomizeDo.getSelfFeedIn());
		customizeInfoDto.setRegularSoc(clientCustomizeDo.getRegularSoc());
		customizeInfoDto.setRegularEpsBat(clientCustomizeDo.getRegularEpsBat());
		customizeInfoDto.setRegularFeedIn(clientCustomizeDo.getRegularFeedIn());
		customizeInfoDto.setBackupSoc(clientCustomizeDo.getBackupSoc());
		customizeInfoDto.setBackupEpsBat(clientCustomizeDo.getBackupEpsBat());
		customizeInfoDto.setBackupFeedIn(clientCustomizeDo.getBackupFeedIn());
		customizeInfoDto.setRegion(clientCustomizeDo.getRegion());
		customizeInfoDto.setAutoStrategy(clientCustomizeDo.getAutoStrategy());
		customizeInfoDto.setAutoHomeStrategy(clientCustomizeDo.getAutoHomeStrategy());
		customizeInfoDto.setStrategyMode(clientCustomizeDo.getStrategyMode());
		customizeInfoDto.setDefChargePower(clientCustomizeDo.getDefChargePower());
		customizeInfoDto.setDefDischargePower(clientCustomizeDo.getDefDischargePower());
		customizeInfoDto.setPriceImportType(clientCustomizeDo.getPriceImportType());
		customizeInfoDto.setPurchaseTax(clientCustomizeDo.getPurchaseTax());
		customizeInfoDto.setPriceGap(clientCustomizeDo.getPriceGap());
		customizeInfoDto.setFeedInProhibition(clientCustomizeDo.getFeedInProhibition());
		process(clientCustomizeDo, customizeInfoDto);
	}

	/** 自定义持久化数据结构转换为list */
	private static void process(ClientCustomizeDo clientCustomizeDo, CustomizeInfoDto customizeInfoDto) {
		List<ChargingStructDTO> chargeList = new ArrayList<>(12);
		List<ChargingStructDTO> dischargeList = new ArrayList<>(12);
		JSONObject jsonItem = JSONUtil.parseObj(clientCustomizeDo);
		for (int i = 1; i <= 12; i++) {
			chargeList.add(new ChargingStructDTO()
					.withStartHour(jsonItem.getInt("chargeStartHour" + i))
					.withStartMinute(jsonItem.getInt("chargeStartMinute" + i))
					.withEndHour(jsonItem.getInt("chargeEndHour" + i))
					.withEndMinute(jsonItem.getInt("chargeEndMinute" + i))
					.withPower(jsonItem.getInt("chargePower" + i))
					.withAbandonPv(jsonItem.getInt("chargeAbandonPv" + i)));
			dischargeList.add(new ChargingStructDTO()
					.withStartHour(jsonItem.getInt("dischargeStartHour" + i))
					.withStartMinute(jsonItem.getInt("dischargeStartMinute" + i))
					.withEndHour(jsonItem.getInt("dischargeEndHour" + i))
					.withEndMinute(jsonItem.getInt("dischargeEndMinute" + i))
					.withPower(jsonItem.getInt("dischargePower" + i))
					.withAbandonPv(jsonItem.getInt("dischargeAbandonPv" + i)));
		}
		customizeInfoDto.setChargingList(chargeList);
		customizeInfoDto.setDischargingList(dischargeList);
	}

}
