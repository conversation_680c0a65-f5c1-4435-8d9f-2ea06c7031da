package com.weihengtech.ecos.model.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("client_ele_price_retailer")
public class ClientElePriceRetailerDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 家庭Id
     */
    private Long homeId;

    /**
     * 零售商
     */
    private Integer retailer;

    /**
     * 平台token
     */
    private String token;

    /**
     * 平台homeId
     */
    private String retailerHomeId;

    /**
     * 账户
     */
    private String account;


}
