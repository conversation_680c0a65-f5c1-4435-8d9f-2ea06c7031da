package com.weihengtech.ecos.model.vos.customize;

import cn.hutool.core.collection.CollUtil;
import com.weihengtech.ecos.common.exception.EcosException;
import com.weihengtech.ecos.common.exception.EcosExceptionEnum;
import com.weihengtech.ecos.model.dos.ClientCustomizeDo;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import com.weihengtech.ecos.utils.NumberUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.val;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ApiModel(value = "自定义配置信息入参")
public class CustomizeInfoVo {

	@ApiModelProperty(name = "deviceId", value = "设备id", required = true)
	@NotBlank(message = "err.not.blank")
	private String deviceId;

	@Min(value = 10)
	@Max(value = 100)
	private Integer minCapacity;

	@Min(value = 0)
	@Max(value = 2)
	@ApiModelProperty(name = "chargeUserMode", value = "selfPowered = 0;loadShifting = 1;backup = 2", required = true)
	private Integer chargeUseMode;

	@Min(value = 0)
	@Max(value = 23)
	private Integer charge1StartTimeHour;

	@Min(value = 0)
	@Max(value = 60)
	private Integer charge1StartTimeMinute;

	@Min(value = 0)
	@Max(value = 23)
	private Integer charge1EndTimeHour;

	@Min(value = 0)
	@Max(value = 60)
	private Integer charge1EndTimeMinute;

	private Integer charge1Power;

	private Integer charge1AbandonPv;

	@Min(value = 0)
	@Max(value = 23)
	private Integer charge2StartTimeHour;

	@Min(value = 0)
	@Max(value = 60)
	private Integer charge2StartTimeMinute;

	@Min(value = 0)
	@Max(value = 23)
	private Integer charge2EndTimeHour;

	@Min(value = 0)
	@Max(value = 60)
	private Integer charge2EndTimeMinute;

	private Integer charge2Power;

	private Integer charge2AbandonPv;

	@Min(value = 0)
	@Max(value = 23)
	private Integer discharge1StartTimeHour;

	@Min(value = 0)
	@Max(value = 60)
	private Integer discharge1StartTimeMinute;

	@Min(value = 0)
	@Max(value = 23)
	private Integer discharge1EndTimeHour;

	@Min(value = 0)
	@Max(value = 60)
	private Integer discharge1EndTimeMinute;

	private Integer discharge1Power;

	private Integer discharge1AbandonPv;

	@Min(value = 0)
	@Max(value = 23)
	private Integer discharge2StartTimeHour;

	@Min(value = 0)
	@Max(value = 60)
	private Integer discharge2StartTimeMinute;

	@Min(value = 0)
	@Max(value = 23)
	private Integer discharge2EndTimeHour;

	@Min(value = 0)
	@Max(value = 60)
	private Integer discharge2EndTimeMinute;

	private Integer discharge2Power;

	private Integer discharge2AbandonPv;

	@Min(value = 0)
	@Max(value = 100)
	private Integer maxFeedIn;

	@Min(value = 10)
	@Max(value = 100)
	private Integer epsBatteryMin;

	@Min(value = 0)
	@Max(value = 1)
	private Integer dischargeToGridFlag;

	@ApiModelProperty(name = "region", value = "电价地区")
	private String region;
	@ApiModelProperty(name = "autoStrategy", value = "开启自动策略")
	private Integer autoStrategy;
	@ApiModelProperty(name = "autoHomeStrategy", value = "开启家庭联动自动策略")
	private Integer autoHomeStrategy;
	@ApiModelProperty(name = "strategyMode", value = "策略模式")
	private Integer strategyMode;
	@ApiModelProperty(name = "defChargePower", value = "自定义充电功率")
	private Integer defChargePower;
	@ApiModelProperty(name = "defDischargePower", value = "自定义放电功率")
	private Integer defDischargePower;
	@ApiModelProperty(name = "timezone", value = "时区")
	private String timezone;
	@ApiModelProperty(name = "priceImportType", value = "电价导入类型：0：自定义|1：家庭电价")
	private Integer priceImportType = 0;
	@ApiModelProperty(name = "purchaseTax", value = "购电税费", required = true)
	private BigDecimal purchaseTax;
	@ApiModelProperty(name = "priceGap", value = "峰谷价差")
	private BigDecimal priceGap;
	@ApiModelProperty(name = "feedInProhibition", value = "禁止馈网开关")
	private Integer feedInProhibition;

	private List<ChargingStructDTO> chargingList;
	private List<ChargingStructDTO> dischargingList;

	/** 构建透传写指令值 */
	public List<Integer> buildPostParams(ClientCustomizeDo clientCustomizeDo) {
		List<Integer> params = new ArrayList<>(Collections.nCopies(27, 0));

		val selfPowered = 0;
		val loadShifting = 1;
		val backup = 2;

		switch (this.chargeUseMode) {
			case selfPowered:
				params.set(0, 4);
				params.set(1, NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, true));
				params.set(2, selfPowered);
				clientCustomizeDo.setBatteryMin(NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, true));
				clientCustomizeDo.setChargeMode(selfPowered);
				break;
			case loadShifting:
				NumberUtil.validChargeTimeCross(this);
				setListParam(params, minCapacity, this);
				setClientCustomizeDo(clientCustomizeDo, minCapacity, this);
				break;
			case backup:
				params.set(0, 4);
				params.set(1, NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, false));
				params.set(2, backup);
				clientCustomizeDo.setBatteryMin(NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, false));
				clientCustomizeDo.setChargeMode(backup);
				break;
			default:
				throw new EcosException(EcosExceptionEnum.INVALID_PARAM);
		}
		return params;
	}

	/** 构建二充二放指令值 */
	private void setListParam(List<Integer> params, Integer minCapacity, CustomizeInfoVo customizeInfoVo) {
		params.set(0, 0);
		params.set(1, NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, false));
		params.set(2, 1);
		params.set(7, NumberUtil.validIntegerValue(customizeInfoVo.getCharge1StartTimeHour(), 0, 23, 0, true));
		params.set(8, NumberUtil.validIntegerValue(customizeInfoVo.getCharge1StartTimeMinute(), 0, 59, 0, true));
		params.set(9, NumberUtil.validIntegerValue(customizeInfoVo.getCharge1EndTimeHour(), 0, 23, 0, true));
		params.set(10, NumberUtil.validIntegerValue(customizeInfoVo.getCharge1EndTimeMinute(), 0, 59, 0, true));
		params.set(11, Optional.ofNullable(customizeInfoVo.getCharge1Power()).orElse(0));
		params.set(12, NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1StartTimeHour(), 0, 23, 0, true));
		params.set(13, NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1StartTimeMinute(), 0, 59, 0, true));
		params.set(14, NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1EndTimeHour(), 0, 23, 0, true));
		params.set(15, NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1EndTimeMinute(), 0, 59, 0, true));
		params.set(16, Optional.ofNullable(customizeInfoVo.getDischarge1Power()).orElse(0));
		params.set(17, NumberUtil.validIntegerValue(customizeInfoVo.getCharge2StartTimeHour(), 0, 23, 0, true));
		params.set(18, NumberUtil.validIntegerValue(customizeInfoVo.getCharge2StartTimeMinute(), 0, 59, 0, true));
		params.set(19, NumberUtil.validIntegerValue(customizeInfoVo.getCharge2EndTimeHour(), 0, 23, 0, true));
		params.set(20, NumberUtil.validIntegerValue(customizeInfoVo.getCharge2EndTimeMinute(), 0, 59, 0, true));
		params.set(21, Optional.ofNullable(customizeInfoVo.getCharge2Power()).orElse(0));
		params.set(22, NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2StartTimeHour(), 0, 23, 0, true));
		params.set(23, NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2StartTimeMinute(), 0, 59, 0, true));
		params.set(24, NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2EndTimeHour(), 0, 23, 0, true));
		params.set(25, NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2EndTimeMinute(), 0, 59, 0, true));
		params.set(26, Optional.ofNullable(customizeInfoVo.getDischarge2Power()).orElse(0));
	}

	/** 构建自定义持久化数据结构 */
	private void setClientCustomizeDo(ClientCustomizeDo clientCustomizeDo, Integer minCapacity, CustomizeInfoVo customizeInfoVo) {
		clientCustomizeDo.setBatteryMin(NumberUtil.validIntegerValue(minCapacity, 10, 100, 10, false));

		clientCustomizeDo.setChargeMode(1);
		clientCustomizeDo.setChargeStartHour1(
				NumberUtil.validIntegerValue(customizeInfoVo.getCharge1StartTimeHour(), 0, 23, 0, true));
		clientCustomizeDo.setChargeStartMinute1(
				NumberUtil.validIntegerValue(customizeInfoVo.getCharge1StartTimeMinute(), 0, 59, 0, true));
		clientCustomizeDo.setChargeEndHour1(
				NumberUtil.validIntegerValue(customizeInfoVo.getCharge1EndTimeHour(), 0, 23, 0, true));
		clientCustomizeDo.setChargeEndMinute1(
				NumberUtil.validIntegerValue(customizeInfoVo.getCharge1EndTimeMinute(), 0, 59, 0, true));

		clientCustomizeDo.setChargeStartHour2(
				NumberUtil.validIntegerValue(customizeInfoVo.getCharge2StartTimeHour(), 0, 23, 0, true));
		clientCustomizeDo.setChargeStartMinute2(
				NumberUtil.validIntegerValue(customizeInfoVo.getCharge2StartTimeMinute(), 0, 59, 0, true));
		clientCustomizeDo.setChargeEndHour2(
				NumberUtil.validIntegerValue(customizeInfoVo.getCharge2EndTimeHour(), 0, 23, 0, true));
		clientCustomizeDo.setChargeEndMinute2(
				NumberUtil.validIntegerValue(customizeInfoVo.getCharge2EndTimeMinute(), 0, 59, 0, true));

		clientCustomizeDo.setDischargeStartHour1(
				NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1StartTimeHour(), 0, 23, 0, true));
		clientCustomizeDo.setDischargeStartMinute1(
				NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1StartTimeMinute(), 0, 59, 0, true));
		clientCustomizeDo.setDischargeEndHour1(
				NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1EndTimeHour(), 0, 23, 0, true));
		clientCustomizeDo.setDischargeEndMinute1(
				NumberUtil.validIntegerValue(customizeInfoVo.getDischarge1EndTimeMinute(), 0, 59, 0, true));

		clientCustomizeDo.setDischargeStartHour2(
				NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2StartTimeHour(), 0, 23, 0, true));
		clientCustomizeDo.setDischargeStartMinute2(
				NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2StartTimeMinute(), 0, 59, 0, true));
		clientCustomizeDo.setDischargeEndHour2(
				NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2EndTimeHour(), 0, 23, 0, true));
		clientCustomizeDo.setDischargeEndMinute2(
				NumberUtil.validIntegerValue(customizeInfoVo.getDischarge2EndTimeMinute(), 0, 59, 0, true));
	}

	/** 构建透传写3-12充放的指令值 */
	public List<Integer> buildExtraParams(ClientCustomizeDo clientCustomizeDo) {
		// 模式不为负载平衡时，需清空充放电数据
		if (clientCustomizeDo.getChargeMode() != 1) {
			return new ArrayList<>(Collections.nCopies(100, 0));
		}
		List<ChargingStructDTO> chargingList = this.getChargingList() == null ? Collections.emptyList() : this.getChargingList();
		List<ChargingStructDTO> dischargingList = this.getDischargingList() == null ? Collections.emptyList() : this.getDischargingList();
		if (chargingList.size() < 3 && dischargingList.size() < 3 && clientCustomizeDo.isEmptyExtraParam()) {
			return Collections.emptyList();
		}
		List<Integer> extraParams = new ArrayList<>(100);
		Class<? extends ChargingStructDTO> clazz = ChargingStructDTO.class;
		try {
			Method getStartHour = clazz.getMethod("getStartHour");
			Method getStartMinute = clazz.getMethod("getStartMinute");
			Method getEndHour = clazz.getMethod("getEndHour");
			Method getEndMinute = clazz.getMethod("getEndMinute");
			Method getPower = clazz.getMethod("getPower");
			int i = 2;
			while (i < Math.max(chargingList.size(), dischargingList.size())) {
				if (i < chargingList.size()) {
					ChargingStructDTO chargingStruct = chargingList.get(i);
					extraParams.add((Integer) getStartHour.invoke(chargingStruct));
					extraParams.add((Integer) getStartMinute.invoke(chargingStruct));
					extraParams.add((Integer) getEndHour.invoke(chargingStruct));
					extraParams.add((Integer) getEndMinute.invoke(chargingStruct));
					extraParams.add(getPower.invoke(chargingStruct) == null ? 0 : (Integer) getPower.invoke(chargingStruct));
				} else {
					for (int i1 = 0; i1 < 5; i1++) {
						extraParams.add(0);
					}
				}
				if (i < dischargingList.size()) {
					ChargingStructDTO dischargingStruct = dischargingList.get(i);
					extraParams.add((Integer) getStartHour.invoke(dischargingStruct));
					extraParams.add((Integer) getStartMinute.invoke(dischargingStruct));
					extraParams.add((Integer) getEndHour.invoke(dischargingStruct));
					extraParams.add((Integer) getEndMinute.invoke(dischargingStruct));
					extraParams.add(getPower.invoke(dischargingStruct) == null ? 0 : (Integer) getPower.invoke(dischargingStruct));
				} else {
					for (int i1 = 0; i1 < 5; i1++) {
						extraParams.add(0);
					}
				}
				i ++;
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		int size = extraParams.size();
		for (int i = 0; i < 100 - size; i++) {
			extraParams.add(0);
		}
		return extraParams;
	}

	public List<Integer> buildAbandonPvParams(ClientCustomizeDo clientCustomizeDo) {
		// 模式不为负载平衡时，需清空充放电数据
		if (clientCustomizeDo.getChargeMode() != 1) {
			return new ArrayList<>(Collections.nCopies(24, 0));
		}
		List<ChargingStructDTO> chargingList = this.getChargingList() == null ? Collections.emptyList() : this.getChargingList();
		List<ChargingStructDTO> dischargingList = this.getDischargingList() == null ? Collections.emptyList() : this.getDischargingList();
		if (CollUtil.isEmpty(chargingList) && CollUtil.isEmpty(dischargingList)) {
			return Collections.emptyList();
		}
		long count = chargingList.stream()
				.filter(i -> i.getAbandonPv() != null)
				.count();
		long disCount = dischargingList.stream()
				.filter(i -> i.getAbandonPv() != null)
				.count();
		if (count == 0 && disCount == 0) {
			return Collections.emptyList();
		}
		List<Integer> abandonPvParams = new ArrayList<>(24);
		Class<? extends ChargingStructDTO> clazz = ChargingStructDTO.class;
		try {
			Method getAbandonPv = clazz.getMethod("getAbandonPv");
			int i = 0;
			while (i < Math.max(chargingList.size(), dischargingList.size())) {
				if (i < chargingList.size()) {
					ChargingStructDTO chargingStruct = chargingList.get(i);
					abandonPvParams.add((Integer) getAbandonPv.invoke(chargingStruct));
				} else {
					abandonPvParams.add(0);
				}
				if (i < dischargingList.size()) {
					ChargingStructDTO dischargingStruct = dischargingList.get(i);
					abandonPvParams.add((Integer) getAbandonPv.invoke(dischargingStruct));
				} else {
					abandonPvParams.add(0);
				}
				i ++;
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		int size = abandonPvParams.size();
		for (int i = 0; i < 24 - size; i++) {
			abandonPvParams.add(0);
		}
		return abandonPvParams;
	}
}
