package com.weihengtech.ecos.model.dtos.customize;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "customized页面聚合回参")
public class CustomizeInfoEzDto {

	private Integer minCapacity;

	@ApiModelProperty(name = "chargeUseMode", value = "0: selfPowered; 1: loadShifting; 2:  backup", required = true)
	private Integer chargeUseMode;

	private Integer maxFeedIn;

	private Integer epsBatteryMin;

	private Integer dischargeToGridFlag;

	// 自发自用SOC
	private Integer selfSoc;
	// 自发自用离网预留SOC
	private Integer selfEpsBat;
	// 自发自用馈网功率比例
	private Integer selfFeedIn;
	// 定时充放SOC
	private Integer regularSoc;
	// 定时充放离网预留SOC
	private Integer regularEpsBat;
	// 定时充放馈网功率比例
	private Integer regularFeedIn;
	// 备电预留SOC
	private Integer backupSoc;
	// 备电离网预留SOC
	private Integer backupEpsBat;
	// 备电预留馈网功率比例
	private Integer backupFeedIn;
	// ems软件版本
	private String emsSoftwareVersion;
	// dsp软件版本
	private String dsp1SoftwareVersion;
	// 额定功率
	private String ratedPower;
	// 电价地区
	private String region;
	// 开启自动策略
	private Integer autoStrategy;
	// 开启家庭联动自动策略
	private Integer autoHomeStrategy;
	// 策略模式
	private Integer strategyMode;
	// 自定义充电功率
	private Integer defChargePower;
	// 自定义放电功率
	private Integer defDischargePower;
	// 电价导入类型：0：自定义|1：家庭电价
	private Integer priceImportType;
	// 购电税费
	private BigDecimal purchaseTax;
	// 峰谷价差
	private BigDecimal priceGap;

	private List<ChargingStructDTO> chargingList;

	private List<ChargingStructDTO> dischargingList;
}
