package com.weihengtech.ecos.model.vos.price;

import com.weihengtech.ecos.consts.RequestConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:27
 * @version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OctopusEleVO {

	@ApiModelProperty(name = "operationName", value = "操作名称", required = true)
	private String operationName = "getPropertiesForMeterPoints";

	@ApiModelProperty(name = "variables", value = "变量", required = true)
	private Variable variables;

	@ApiModelProperty(name = "query", value = "查询语句", required = true)
	private String query = RequestConstants.OCTOPUS_ELE_PARAM;

	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	@Data
	public static class Variable {

		@ApiModelProperty(name = "accountNumber", value = "户号", required = true)
		private String accountNumber;

		@ApiModelProperty(name = "propertiesActiveFrom", value = "生效时间", required = true)
		private String propertiesActiveFrom;
	}
}
