<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weihengtech.ecos.dao.ClientHomeDeviceMapper">

    <select id="getAllMasterHomeDevice" resultType="com.weihengtech.ecos.model.dos.ClientHomeDeviceDo">
        SELECT d.*
        from client_home_device d
                 JOIN client_home h
                      on h.id = d.home_id
        where h.home_type = 1
        and h.timezone in
        <foreach collection="timezoneList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
