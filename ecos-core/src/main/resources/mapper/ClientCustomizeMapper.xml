<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weihengtech.ecos.dao.ClientCustomizeMapper">

    <select id="getAutoStrategyDevices" resultType="com.weihengtech.ecos.model.dtos.customize.StrategyCustomizeDTO">
        SELECT d.home_id homeId,
            d.device_id deviceId,
            c.battery_min batteryMin,
            c.max_feed_in maxFeedIn,
            c.region,
            c.auto_strategy autoStrategy,
            c.auto_home_strategy autoHomeStrategy,
            c.strategy_mode strategyMode,
            c.def_charge_power defChargePower,
            c.def_discharge_power defDischargePower,
            c.timezone,
            c.price_import_type priceImportType,
            c.purchase_tax purchaseTax,
            c.price_gap priceGap
        from client_customize c
        join client_home_device d
        on d.device_id = c.device_id
        join client_home h
        on h.id = d.home_id
        where h.timezone in
        <foreach collection="timezoneList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and (c.strategy_mode > 0
        or c.auto_strategy = 1)
    </select>

    <select id="getAutoStrategyDevicesByIds" resultType="com.weihengtech.ecos.model.dos.ClientCustomizeDo">
        SELECT *
        from client_customize
        where device_id in
        <foreach collection="deviceIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and (auto_strategy = 1 or strategy_mode > 0)
    </select>

    <select id="getFeedInProhibitionDevices" resultType="com.weihengtech.ecos.model.dtos.customize.StrategyCustomizeDTO">
        SELECT d.home_id homeId,
            d.device_id deviceId,
            c.battery_min batteryMin,
            c.max_feed_in maxFeedIn,
            c.region,
            c.auto_strategy autoStrategy,
            c.auto_home_strategy autoHomeStrategy,
            c.strategy_mode strategyMode,
            c.def_charge_power defChargePower,
            c.def_discharge_power defDischargePower,
            c.timezone,
            c.price_import_type priceImportType,
            c.purchase_tax purchaseTax,
            c.price_gap priceGap
        from client_customize c
        join client_home_device d
        on d.device_id = c.device_id
        join client_home h
        on h.id = d.home_id
        where h.timezone in
        <foreach collection="timezoneList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and c.feed_in_prohibition = 1
    </select>
</mapper>
