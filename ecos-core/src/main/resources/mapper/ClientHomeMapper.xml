<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weihengtech.ecos.dao.ClientHomeMapper">

    <select id="queryHomePriceHomeList" resultType="com.weihengtech.ecos.model.dos.ClientHomeDo">
        SELECT distinct h.*
        from client_home h
        join client_home_device hd
        on hd.home_id = h.id
        where h.ele_price_type is not null
        and h.timezone in
        <foreach collection="timezoneList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and EXISTS (
            SELECT 1
            from client_customize c
            where c.device_id = hd.device_id
            and c.price_import_type = 1
        )
    </select>
</mapper>
