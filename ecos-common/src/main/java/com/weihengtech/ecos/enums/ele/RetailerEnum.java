package com.weihengtech.ecos.enums.ele;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/15 16:01
 */
@Getter
@AllArgsConstructor
public enum RetailerEnum {

    TIBBER(1, "https://api.tibber.com/v1-beta/gql"),
    OCTOPUS(2, "https://api.octopus.energy/v1"),
    NULL(999, "无");

    final int code;

    final String url;

    public static String getUrlByCode(int code) {
        for (RetailerEnum value : RetailerEnum.values()) {
            if (value.code == code) {
                return value.url;
            }
        }
        return null;
    }

    public static RetailerEnum getEnum(int code) {
        for (RetailerEnum e : RetailerEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        return NULL;
    }
}
