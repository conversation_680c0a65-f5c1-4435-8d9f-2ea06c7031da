package com.weihengtech.ecos.enums.ele;

import lombok.Getter;

/**
 * @author: jiahao.jin
 * @create: 2025-08-19 15:40
 * @description: 电价类型详情
 */
@Getter
public enum ElePriceTypeDetailEnum {

    FIXED(0, "固定电价"),
    TIME_OF_USE(1, "分时电价"),
    INTERVAL_15MIN(2, "15分钟电价"),
    INTERVAL_30MIN(3, "30分钟电价"),
    INTERVAL_1H(4, "1小时电价");

    private final int code;
    private final String name;

    ElePriceTypeDetailEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

}
