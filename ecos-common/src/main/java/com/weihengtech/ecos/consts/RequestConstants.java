package com.weihengtech.ecos.consts;

/**
 * @program: ecos-server
 * @description:
 * @author: jiahao.jin
 * @create: 2024-06-25 19:12
 **/
public interface RequestConstants {

    Integer HUB_REQUEST_MILLISECOND_TIMES = 10000;

    Integer DATABASE_REQUEST_MILLISECOND_TIMES = 20000;

    String TIBBER_HOME_PARAM = "{\"query\":\"{\\n  viewer {\\n    homes {\\n      id\\n      timeZone\\n      type\\n      appNickname\\n      appAvatar\\n      address {\\n        address1\\n        address2\\n        address3\\n      }\\n    }\\n  }\\n}\"}";

    String TIBBER_ELE_PARAM = "{\"query\":\"query {\\n  viewer {\\n    home(id: \\\"%s\\\") {\\n      timeZone\\n      currentSubscription {\\n        priceInfo {\\n          %s {\\n            total\\n            energy\\n            tax\\n            startsAt\\n            currency\\n          }\\n        }\\n      }\\n    }\\n  }\\n}\\n\"}";

    String OCTOPUS_AUTH_PARAM = "mutation ObtainKrakenToken($input: ObtainJSONWebTokenInput!) {\n" +
            "  obtainKrakenToken(input: $input) {\n" +
            "    token\n" +
            "    payload\n" +
            "    refreshToken\n" +
            "    refreshExpiresIn\n" +
            "  }\n" +
            "}\n";

    String OCTOPUS_ELE_PARAM = "query getPropertiesForMeterPoints(\n" +
            "  $accountNumber: String!\n" +
            "  $propertiesActiveFrom: DateTime\n" +
            ") {\n" +
            "  account(accountNumber: $accountNumber) {\n" +
            "    properties(activeFrom: $propertiesActiveFrom) {\n" +
            "      electricityMeterPoints {\n" +
            "        __typename\n" +
            "        agreements(excludeFuture: false) {\n" +
            "          id\n" +
            "          validFrom\n" +
            "          validTo\n" +
            "          isRevoked\n" +
            "          unitRateUplifts {\n" +
            "            unitRateUplift\n" +
            "            __typename\n" +
            "          }\n" +
            "          tariff {\n" +
            "            __typename\n" +
            "            ... on TariffType {\n" +
            "              standingCharge\n" +
            "              preVatStandingCharge\n" +
            "              displayName\n" +
            "              fullName\n" +
            "              __typename\n" +
            "            }\n" +
            "            ... on StandardTariff {\n" +
            "              unitRate\n" +
            "              preVatUnitRate\n" +
            "              __typename\n" +
            "            }\n" +
            "            ... on DayNightTariff {\n" +
            "              dayRate\n" +
            "              preVatDayRate\n" +
            "              nightRate\n" +
            "              preVatNightRate\n" +
            "              __typename\n" +
            "            }\n" +
            "            ... on HalfHourlyTariff {\n" +
            "              unitRates {\n" +
            "                value\n" +
            "                validFrom\n" +
            "                validTo\n" +
            "                __typename\n" +
            "              }\n" +
            "              tariffCode\n" +
            "              productCode\n" +
            "              __typename\n" +
            "            }\n" +
            "            ... on PrepayTariff {\n" +
            "              unitRate\n" +
            "              preVatUnitRate\n" +
            "              __typename\n" +
            "            }\n" +
            "          }\n" +
            "          __typename\n" +
            "        }\n" +
            "      }\n" +
            "    }\n" +
            "    __typename\n" +
            "  }\n" +
            "}\n";
}
